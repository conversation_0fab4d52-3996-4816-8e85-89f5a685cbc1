{"buildFiles": ["C:\\Users\\<USER>\\fvm\\versions\\stable\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\sintesy-me-app\\android\\app\\.cxx\\RelWithDebInfo\\6r3e465o\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\sintesy-me-app\\android\\app\\.cxx\\RelWithDebInfo\\6r3e465o\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}