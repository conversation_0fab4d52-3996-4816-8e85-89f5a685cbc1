                        -HC:\Users\<USER>\fvm\versions\stable\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-<PERSON>ANDROID_PLATFORM=android-24
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\sintesy-me-app\build\app\intermediates\cxx\RelWithDebInfo\6r3e465o\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\sintesy-me-app\build\app\intermediates\cxx\RelWithDebInfo\6r3e465o\obj\x86
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BC:\Users\<USER>\sintesy-me-app\android\app\.cxx\RelWithDebInfo\6r3e465o\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2