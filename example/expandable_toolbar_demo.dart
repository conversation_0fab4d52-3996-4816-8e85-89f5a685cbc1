import 'package:flutter/material.dart';
import 'package:sintesy_app/widgets/custom_bottom_navigation_bar.dart';
import 'package:sintesy_app/themes/light_theme.dart';

void main() {
  runApp(const ExpandableToolbarDemo());
}

class ExpandableToolbarDemo extends StatelessWidget {
  const ExpandableToolbarDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Expandable Toolbar Demo',
      theme: lightTheme(),
      home: const DemoScreen(),
    );
  }
}

class DemoScreen extends StatefulWidget {
  const DemoScreen({super.key});

  @override
  State<DemoScreen> createState() => _DemoScreenState();
}

class _DemoScreenState extends State<DemoScreen> {
  String _lastAction = 'Nenhuma ação executada';
  double _screenWidth = 400;

  void _showAction(String action) {
    setState(() {
      _lastAction = action;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Ação: $action'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Demo: Toolbar Expansível'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
      ),
      body: Column(
        children: [
          // Controle de largura da tela
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Simular largura da tela: ${_screenWidth.round()}px',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Slider(
                  value: _screenWidth,
                  min: 280,
                  max: 500,
                  divisions: 22,
                  label: '${_screenWidth.round()}px',
                  onChanged: (value) {
                    setState(() {
                      _screenWidth = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                Text(
                  _getScreenSizeDescription(),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          
          // Área principal
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.touch_app,
                    size: 64,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Teste a toolbar abaixo!',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Última ação: $_lastAction',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 24),
                  _buildInstructions(),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        child: MediaQuery(
          data: MediaQuery.of(context).copyWith(
            size: Size(_screenWidth, MediaQuery.of(context).size.height),
          ),
          child: CustomBottomNavigationBar(
            onRecordTap: () => _showAction('GRAVAÇÃO'),
            onYoutubeTap: () => _showAction('YOUTUBE'),
            onMeetingTap: () => _showAction('REUNIÃO'),
            onNoteTap: () => _showAction('ANOTAÇÃO'),
            onFileTap: () => _showAction('ARQUIVO'),
          ),
        ),
      ),
    );
  }

  String _getScreenSizeDescription() {
    if (_screenWidth >= 400) {
      return '📱 Tela grande - Todos os botões visíveis';
    } else if (_screenWidth >= 350) {
      return '📱 Tela média - 3 botões + expansão';
    } else {
      return '📱 Tela pequena - 2 botões + expansão';
    }
  }

  Widget _buildInstructions() {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 32),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
              const SizedBox(width: 8),
              Text(
                'Como testar:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            '• Ajuste a largura da tela com o slider\n'
            '• Em telas menores, clique em "MAIS" para expandir\n'
            '• Clique em "MENOS" para recolher\n'
            '• Teste todos os botões para ver as ações',
            style: TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }
}
