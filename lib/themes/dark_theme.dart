import 'package:flutter/material.dart';
import 'light_theme.dart'; // Import para usar a classe AppColors

ThemeData darkTheme() {
  const darkAppColors = AppColors(
    // Botões de gravação - mantém cores vibrantes para destaque
    recordingButtonGradientStart: Color(0xFFFF5252), // Vermelho mais suave
    recordingButtonGradientEnd: Color(0xFFFF1744),
    recordingButtonBorder: Color(0xFFFF1744),

    // Botões de reprodução - azul mais vibrante para dark mode
    playButtonGradientStart: Color(0xFF42A5F5),
    playButtonGradientEnd: Color(0xFF1976D2),
    playButtonBorder: Color(0xFF42A5F5),

    // Texto e elementos de gravação
    recordingText: Color(0xFFFF5252),
    waveformColor: Color(0xFFFF5252),

    // Barras de progresso - melhor contraste
    progressBackground: Color(0xFF424242),
    progressIndicator: Color(0xFF42A5F5),

    // Cores de status
    successColor: Color(0xFF4CAF50), // Verde mais vibrante
    warningColor: Color(0xFFFF9800), // Laranja para melhor visibilidade

    // Elementos estruturais
    shadowColor: Color(0xFF000000), // Sombra mais intensa para dark mode
    borderColor: Color(0xFF616161), // Cinza médio para bordas

    // Containers e superfícies
    containerGradientStart: Color(0xFF2E2E2E), // Cinza escuro mais quente
    containerGradientEnd: Color(0xFF2E2E2E),

    // Divisores e elementos secundários
    dividerColor: Color(0xFF424242),
    subtitleText: Color(0xFFBDBDBD), // Cinza claro para melhor legibilidade
    linkColor: Color(0xFF42A5F5), // Azul claro para links

    // Gradientes de fundo
    backgroundGradientStart: Color(0xFF1E1E1E), // Preto suave
    backgroundGradientEnd: Color(0xFF1E1E1E),
    backgroundGradientMiddle: Color(0xFF2E2E2E),
  );

  return ThemeData(
    colorScheme: const ColorScheme(
      brightness: Brightness.dark,
      primary: Color(0xFF42A5F5), // Azul claro como cor principal
      secondary: Color(0xFFFFFFFF), // Branco para destaque
      tertiary: Color(0xFF9E9E9E), // Cinza médio
      surface: Color(0xFF2E2E2E), // Superfícies em cinza escuro quente
      error: Color(0xFFFF5252), // Vermelho suave para erros
      onPrimary: Color(0xFF000000), // Texto preto sobre azul
      onSecondary: Color(0xFF000000), // Texto preto sobre branco
      onSurface: Color(0xFFFFFFFF), // Texto branco sobre superfícies
      onError: Color(0xFFFFFFFF), // Texto branco sobre erro
      // Cores adicionais para melhor suporte
      background: Color(0xFF1E1E1E), // Fundo principal
      onBackground: Color(0xFFFFFFFF), // Texto sobre fundo
      surfaceVariant: Color(0xFF424242), // Variação de superfície
      onSurfaceVariant: Color(0xFFE0E0E0), // Texto sobre variação de superfície
    ),
    extensions: const <ThemeExtension<dynamic>>[
      darkAppColors,
    ],

    // AppBar com melhor contraste
    appBarTheme: const AppBarTheme(
      backgroundColor: Color(0xFF1E1E1E), // Fundo escuro consistente
      surfaceTintColor: Colors.transparent, // Remove mudança de cor no scroll
      foregroundColor: Color(0xFFFFFFFF), // Texto branco
      iconTheme: IconThemeData(color: Color(0xFFFFFFFF)),
      titleTextStyle: TextStyle(
        color: Color(0xFFFFFFFF),
        fontSize: 20,
        fontWeight: FontWeight.w500,
      ),
      elevation: 0, // AppBar plana para design moderno
    ),

    // Fundo principal
    scaffoldBackgroundColor: const Color(0xFF1E1E1E),

    // Navegação inferior
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Color(0xFF2E2E2E),
      type: BottomNavigationBarType.fixed,
      selectedItemColor: Color(0xFF42A5F5), // Azul para item selecionado
      unselectedItemColor: Color(0xFF9E9E9E), // Cinza para itens não selecionados
      elevation: 8, // Sombra sutil
    ),

    // Lista de itens
    listTileTheme: const ListTileThemeData(
      titleTextStyle: TextStyle(
        fontWeight: FontWeight.bold,
        color: Color(0xFFFFFFFF),
        fontSize: 18,
      ),
      subtitleTextStyle: TextStyle(
        color: Color(0xFFBDBDBD),
        fontSize: 15,
      ),
      iconColor: Color(0xFF9E9E9E),
    ),

    // Cards e superfícies elevadas
    cardTheme: const CardThemeData(
      color: Color(0xFF2E2E2E),
      elevation: 4,
      shadowColor: Color(0xFF000000),
    ),

    // Divisores
    dividerTheme: const DividerThemeData(
      color: Color(0xFF424242),
      thickness: 1,
    ),

    // SnackBars
    snackBarTheme: const SnackBarThemeData(
      backgroundColor: Color(0xFF42A5F5),
      contentTextStyle: TextStyle(color: Color(0xFFFFFFFF)),
      actionTextColor: Color(0xFFFFFFFF),
    ),

    // Botões
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF42A5F5),
        foregroundColor: const Color(0xFFFFFFFF),
        elevation: 4,
      ),
    ),

    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: const Color(0xFF42A5F5),
      ),
    ),

    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: const Color(0xFF42A5F5),
        side: const BorderSide(color: Color(0xFF42A5F5)),
      ),
    ),

    // Campos de texto
    inputDecorationTheme: const InputDecorationTheme(
      filled: true,
      fillColor: Color(0xFF2E2E2E),
      border: OutlineInputBorder(
        borderSide: BorderSide(color: Color(0xFF616161)),
      ),
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: Color(0xFF616161)),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(color: Color(0xFF42A5F5)),
      ),
      labelStyle: TextStyle(color: Color(0xFFBDBDBD)),
      hintStyle: TextStyle(color: Color(0xFF9E9E9E)),
    ),

    // Switches e checkboxes
    switchTheme: SwitchThemeData(
      thumbColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return const Color(0xFF42A5F5);
        }
        return const Color(0xFF9E9E9E);
      }),
      trackColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return const Color(0xFF42A5F5).withOpacity(0.5);
        }
        return const Color(0xFF616161);
      }),
    ),

    checkboxTheme: CheckboxThemeData(
      fillColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return const Color(0xFF42A5F5);
        }
        return Colors.transparent;
      }),
      checkColor: MaterialStateProperty.all(const Color(0xFFFFFFFF)),
    ),

    // Tabs
    tabBarTheme: const TabBarThemeData(
      labelColor: Color(0xFF42A5F5),
      unselectedLabelColor: Color(0xFF9E9E9E),
      indicatorColor: Color(0xFF42A5F5),
      dividerColor: Color(0xFF424242),
    ),

    // Sliders
    sliderTheme: const SliderThemeData(
      activeTrackColor: Color(0xFF42A5F5),
      inactiveTrackColor: Color(0xFF616161),
      thumbColor: Color(0xFF42A5F5),
      overlayColor: Color(0x2942A5F5),
    ),

    // Progress indicators
    progressIndicatorTheme: const ProgressIndicatorThemeData(
      color: Color(0xFF42A5F5),
      linearTrackColor: Color(0xFF424242),
      circularTrackColor: Color(0xFF424242),
    ),
  );
}