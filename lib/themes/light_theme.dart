import 'package:flutter/material.dart';

// Extensão personalizada para cores específicas do app
@immutable
class AppColors extends ThemeExtension<AppColors> {
  const AppColors({
    required this.recordingButtonGradientStart,
    required this.recordingButtonGradientEnd,
    required this.recordingButtonBorder,
    required this.playButtonGradientStart,
    required this.playButtonGradientEnd,
    required this.playButtonBorder,
    required this.recordingText,
    required this.waveformColor,
    required this.progressBackground,
    required this.progressIndicator,
    required this.successColor,
    required this.warningColor,
    required this.shadowColor,
    required this.borderColor,
    required this.containerGradientStart,
    required this.containerGradientEnd,
    required this.dividerColor,
    required this.subtitleText,
    required this.linkColor,
    required this.backgroundGradientStart,
    required this.backgroundGradientEnd,
    required this.backgroundGradientMiddle,
  });

  final Color recordingButtonGradientStart;
  final Color recordingButtonGradientEnd;
  final Color recordingButtonBorder;
  final Color playButtonGradientStart;
  final Color playButtonGradientEnd;
  final Color playButtonBorder;
  final Color recordingText;
  final Color waveformColor;
  final Color progressBackground;
  final Color progressIndicator;
  final Color successColor;
  final Color warningColor;
  final Color shadowColor;
  final Color borderColor;
  final Color containerGradientStart;
  final Color containerGradientEnd;
  final Color dividerColor;
  final Color subtitleText;
  final Color linkColor;
  final Color backgroundGradientStart;
  final Color backgroundGradientEnd;
  final Color backgroundGradientMiddle;

  @override
  AppColors copyWith({
    Color? recordingButtonGradientStart,
    Color? recordingButtonGradientEnd,
    Color? recordingButtonBorder,
    Color? playButtonGradientStart,
    Color? playButtonGradientEnd,
    Color? playButtonBorder,
    Color? recordingText,
    Color? waveformColor,
    Color? progressBackground,
    Color? progressIndicator,
    Color? successColor,
    Color? warningColor,
    Color? shadowColor,
    Color? borderColor,
    Color? containerGradientStart,
    Color? containerGradientEnd,
    Color? dividerColor,
    Color? subtitleText,
    Color? linkColor,
    Color? backgroundGradientStart,
    Color? backgroundGradientEnd,
    Color? backgroundGradientMiddle,
  }) {
    return AppColors(
      recordingButtonGradientStart: recordingButtonGradientStart ?? this.recordingButtonGradientStart,
      recordingButtonGradientEnd: recordingButtonGradientEnd ?? this.recordingButtonGradientEnd,
      recordingButtonBorder: recordingButtonBorder ?? this.recordingButtonBorder,
      playButtonGradientStart: playButtonGradientStart ?? this.playButtonGradientStart,
      playButtonGradientEnd: playButtonGradientEnd ?? this.playButtonGradientEnd,
      playButtonBorder: playButtonBorder ?? this.playButtonBorder,
      recordingText: recordingText ?? this.recordingText,
      waveformColor: waveformColor ?? this.waveformColor,
      progressBackground: progressBackground ?? this.progressBackground,
      progressIndicator: progressIndicator ?? this.progressIndicator,
      successColor: successColor ?? this.successColor,
      warningColor: warningColor ?? this.warningColor,
      shadowColor: shadowColor ?? this.shadowColor,
      borderColor: borderColor ?? this.borderColor,
      containerGradientStart: containerGradientStart ?? this.containerGradientStart,
      containerGradientEnd: containerGradientEnd ?? this.containerGradientEnd,
      dividerColor: dividerColor ?? this.dividerColor,
      subtitleText: subtitleText ?? this.subtitleText,
      linkColor: linkColor ?? this.linkColor,
      backgroundGradientStart: backgroundGradientStart ?? this.backgroundGradientStart,
      backgroundGradientEnd: backgroundGradientEnd ?? this.backgroundGradientEnd,
      backgroundGradientMiddle: backgroundGradientMiddle ?? this.backgroundGradientMiddle,
    );
  }

  @override
  AppColors lerp(ThemeExtension<AppColors>? other, double t) {
    if (other is! AppColors) {
      return this;
    }
    return AppColors(
      recordingButtonGradientStart: Color.lerp(recordingButtonGradientStart, other.recordingButtonGradientStart, t)!,
      recordingButtonGradientEnd: Color.lerp(recordingButtonGradientEnd, other.recordingButtonGradientEnd, t)!,
      recordingButtonBorder: Color.lerp(recordingButtonBorder, other.recordingButtonBorder, t)!,
      playButtonGradientStart: Color.lerp(playButtonGradientStart, other.playButtonGradientStart, t)!,
      playButtonGradientEnd: Color.lerp(playButtonGradientEnd, other.playButtonGradientEnd, t)!,
      playButtonBorder: Color.lerp(playButtonBorder, other.playButtonBorder, t)!,
      recordingText: Color.lerp(recordingText, other.recordingText, t)!,
      waveformColor: Color.lerp(waveformColor, other.waveformColor, t)!,
      progressBackground: Color.lerp(progressBackground, other.progressBackground, t)!,
      progressIndicator: Color.lerp(progressIndicator, other.progressIndicator, t)!,
      successColor: Color.lerp(successColor, other.successColor, t)!,
      warningColor: Color.lerp(warningColor, other.warningColor, t)!,
      shadowColor: Color.lerp(shadowColor, other.shadowColor, t)!,
      borderColor: Color.lerp(borderColor, other.borderColor, t)!,
      containerGradientStart: Color.lerp(containerGradientStart, other.containerGradientStart, t)!,
      containerGradientEnd: Color.lerp(containerGradientEnd, other.containerGradientEnd, t)!,
      dividerColor: Color.lerp(dividerColor, other.dividerColor, t)!,
      subtitleText: Color.lerp(subtitleText, other.subtitleText, t)!,
      linkColor: Color.lerp(linkColor, other.linkColor, t)!,
      backgroundGradientStart: Color.lerp(backgroundGradientStart, other.backgroundGradientStart, t)!,
      backgroundGradientEnd: Color.lerp(backgroundGradientEnd, other.backgroundGradientEnd, t)!,
      backgroundGradientMiddle: Color.lerp(backgroundGradientMiddle, other.backgroundGradientMiddle, t)!,
    );
  }
}

ThemeData lightTheme() {
  const lightAppColors = AppColors(
    recordingButtonGradientStart: Color.fromARGB(255, 247, 78, 63),
    recordingButtonGradientEnd: Color.fromARGB(255, 255, 65, 65),
    recordingButtonBorder: Color.fromARGB(255, 255, 64, 64),
    playButtonGradientStart: Color(0xFF3FC4F7),
    playButtonGradientEnd: Color(0xFF0C69E8),
    playButtonBorder: Colors.lightBlueAccent,
    recordingText: Colors.red,
    waveformColor: Colors.red,
    progressBackground: Color.fromARGB(255, 240, 240, 240),
    progressIndicator: Colors.blue,
    successColor: Colors.green,
    warningColor: Color.fromARGB(255, 255, 102, 102),
    shadowColor: Colors.black12,
    borderColor: Colors.grey,
    containerGradientStart: Colors.white,
    containerGradientEnd: Colors.white,
    dividerColor: Colors.grey,
    subtitleText: Color.fromARGB(255, 180, 180, 180),
    linkColor: Color.fromARGB(255, 37, 109, 254),
    backgroundGradientStart: Colors.white,
    backgroundGradientEnd: Colors.white,
    backgroundGradientMiddle: Color.fromARGB(255, 240, 240, 240),
  );

  return ThemeData(
    colorScheme: const ColorScheme(
      brightness: Brightness.light,
      primary: Colors.blue, // Cor principal
      secondary: Colors.black, // Cor de destaque
      tertiary: Colors.grey,
      surface: Colors.white, // Cor de fundo para superfícies (como cartões)
      error: Colors.redAccent, // Cor de erro
      onPrimary: Colors.black, // Cor do texto sobre a cor primária
      onSecondary: Colors.white, // Cor do texto sobre a cor secundária
      onSurface: Colors.black, // Cor do texto sobre superfícies
      onError: Colors.white, // Cor do texto sobre a cor de erro
    ),
    extensions: const <ThemeExtension<dynamic>>[
      lightAppColors,
    ],
    appBarTheme: const AppBarTheme(
        backgroundColor: Colors.white, // Cor de fundo da AppBar
        surfaceTintColor: Colors.transparent, // Remove o efeito de mudança de cor no scroll
        foregroundColor: Colors.black, // Texto da AppBar
        iconTheme: IconThemeData(color: Colors.black54),
        titleTextStyle: TextStyle(
          color: Colors.black,
          fontSize: 20,
          fontWeight: FontWeight.w500,
        )),
    scaffoldBackgroundColor: Colors.white, // Define a cor de fundo do Scaffold
    dividerTheme: const DividerThemeData(color: Colors.transparent),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Colors.white,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: Colors.black,
      unselectedItemColor: Colors.black54,
    ),
    listTileTheme: const ListTileThemeData(
      titleTextStyle: TextStyle(
          fontWeight: FontWeight.bold, color: Colors.black, fontSize: 18),
      subtitleTextStyle:
          TextStyle(color: Color.fromARGB(255, 180, 180, 180), fontSize: 15),
    ),
    snackBarTheme: const SnackBarThemeData(
      backgroundColor: Colors.blue,
      contentTextStyle: TextStyle(color: Colors.white),
    ),
  );
}
