import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/models/sintesy_model.dart';
import 'package:sintesy_app/providers/connection_checker.dart';
import 'package:sintesy_app/providers/recording_state.dart';
import 'package:sintesy_app/providers/sintesy_state.dart';
import 'package:sintesy_app/services/handle_task_service.dart';
import 'package:sintesy_app/services/permission_service.dart';
import 'package:sintesy_app/services/recording_service.dart';
import 'package:sintesy_app/services/sintesy_local_crud.dart';
import 'package:sintesy_app/services/sintesy_service.dart';
import 'package:sintesy_app/services/sintesy_sync_service.dart';
import 'package:sintesy_app/services/user_plan_manager.dart';
import 'package:sintesy_app/utils/call_plan.dart';
import 'package:sintesy_app/widgets/audio_waveform.dart';
import 'package:flutter_sound/flutter_sound.dart';
import '../themes/light_theme.dart';
import 'dart:io';
import 'dart:async';

class _FakeProgressDialog extends StatefulWidget {
  final String title;
  final VoidCallback onComplete;

  const _FakeProgressDialog({
    required this.title,
    required this.onComplete,
  });

  @override
  State<_FakeProgressDialog> createState() => _FakeProgressDialogState();
}

class _FakeProgressDialogState extends State<_FakeProgressDialog> {
  double _progress = 0.0;
  late Timer _timer;

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      setState(() {
        if (_progress < 0.95) {
          _progress += (1.0 - _progress) * 0.02;
        } else if (_progress < 0.97) {
          _progress += 0.0001;
        } else {
          _progress = 1.0;
          _timer.cancel();
          widget.onComplete();
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted && Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            }
          });
        }
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final progress = (_progress * 100).toInt();

    return AlertDialog(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(
            value: _progress,
          ),
          const SizedBox(height: 16),
          Text(
            '$progress% - ${widget.title}',
          ),
        ],
      ),
    );
  }
}

class Recorder extends StatefulWidget {
  const Recorder({super.key});

  @override
  State<Recorder> createState() => _RecorderState();
}

class _RecorderState extends State<Recorder> with TickerProviderStateMixin {
  late Animation<double> _animation;
  late RecordingState recordingState;
  late PermissionService permissionService;
  late RecordingService recordingService;
  late HandleTaskService handleTaskService;
  late AnimationController _animationController;
  late SintesyService sintesyService;
  final SintesyState sintesyState = SintesyState();
  SintesySyncService sintesySyncService = SintesySyncService();
  late UserPlanProvider userPlanProvider;
  final SintesyCrud sintesyCrud = SintesyCrud();

  bool _isPreviewMode = false;
  bool _isPlaying = false;
  final FlutterSoundPlayer _audioPlayer = FlutterSoundPlayer();
  SintesyModel? _recordedSintesy;

  @override
  void initState() {
    super.initState();
    recordingState = Provider.of<RecordingState>(context, listen: false);
    recordingService = RecordingService(recordingState, context);
    handleTaskService = HandleTaskService();
    permissionService = PermissionService(context);
    sintesyService = SintesyService();
    userPlanProvider = Provider.of<UserPlanProvider>(context, listen: false);

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.95, end: 1).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    _audioPlayer.openPlayer();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _audioPlayer.closePlayer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final appColors = Theme.of(context).extension<AppColors>()!;

    return Consumer<RecordingState>(
      builder: (context, recordingState, child) {
        if (_isPreviewMode && _recordedSintesy != null) {
          return Container(
            width: 320,
            padding:
                const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10.0),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12.0),
              border: Border.all(
                color: Theme.of(context).colorScheme.tertiary,
                width: 1.0,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Theme.of(context)
                            .colorScheme
                            .primary
                            .withOpacity(0.1),
                      ),
                      child: IconButton(
                        icon: Icon(
                          _isPlaying ? Icons.pause : Icons.play_arrow,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        onPressed: playPreviewAudio,
                      ),
                    ),
                    const SizedBox(width: 20),
                    Container(
                      width: 80,
                      height: 30,
                      alignment: Alignment.center,
                      child: AudioWaveform(
                        isRecording: false,
                        color: appColors.progressIndicator,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 15),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton.icon(
                      onPressed: cancelRecording,
                      icon: Icon(Icons.cancel, color: Theme.of(context).colorScheme.error),
                      label: const Text('Cancelar'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.surface,
                        foregroundColor: Theme.of(context).colorScheme.error,
                        side: BorderSide(color: Theme.of(context).colorScheme.error),
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: sendRecording,
                      icon: const Icon(Icons.save),
                      label: const Text('Salvar'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        }

        return Container(
          width: recordingState.recorderStatus == 'record' ? 300 : 280,
          padding: const EdgeInsets.symmetric(horizontal: 35.0, vertical: 6.0),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12.0),
            border: Border.all(
              color: recordingState.recorderStatus == 'record'
                  ? Theme.of(context).colorScheme.error
                  : Theme.of(context).colorScheme.tertiary,
              width: 1.0,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            mainAxisSize: MainAxisSize.min,
            children: [
              ScaleTransition(
                scale: _animation,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: recordingState.recorderStatus == 'record'
                          ? [
                              appColors.recordingButtonGradientStart,
                              appColors.recordingButtonGradientEnd,
                            ]
                          : [
                              appColors.playButtonGradientStart,
                              appColors.playButtonGradientEnd,
                            ],
                    ),
                    border: Border.all(
                      color: recordingState.recorderStatus == 'record'
                          ? appColors.recordingButtonBorder
                          : appColors.playButtonBorder,
                      width: 3,
                    ),
                  ),
                  child: Center(
                    child: IconButton(
                      icon: recordingState.recorderStatus == 'record' ||
                              recordingState.recorderStatus == 'pause'
                          ? Icon(Icons.square, color: Theme.of(context).colorScheme.onPrimary)
                          : Icon(Icons.mic, color: Theme.of(context).colorScheme.onPrimary),
                      onPressed: onRecordButtonPressed,
                    ),
                  ),
                ),
              ),
              if (recordingState.recorderStatus == 'record' ||
                  recordingState.recorderStatus == 'pause')
                const SizedBox(width: 8.0),
              if (recordingState.recorderStatus == 'record' ||
                  recordingState.recorderStatus == 'pause')
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      width: 1,
                    ),
                  ),
                  child: Center(
                    child: IconButton(
                      icon: recordingState.recorderStatus == 'record'
                          ? Icon(Icons.pause,
                              color: Theme.of(context).colorScheme.secondary)
                          : Icon(Icons.play_arrow,
                              color: Theme.of(context).colorScheme.secondary),
                      onPressed: onPauseButtonPressed,
                    ),
                  ),
                ),
              const SizedBox(width: 20.0),
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    recordingState.recorderStatus == 'record'
                        ? 'Ouvindo...'
                        : recordingState.recorderStatus == 'pause'
                            ? 'Continuar'
                            : 'Gravar aúdio',
                    style: TextStyle(
                      fontSize: 18.0,
                      fontWeight: FontWeight.bold,
                      color: recordingState.recorderStatus == 'record'
                          ? appColors.recordingText
                          : Theme.of(context).colorScheme.secondary,
                    ),
                  ),
                  if (recordingState.recorderStatus == 'record' ||
                      recordingState.recorderStatus == 'pause')
                    Row(
                      children: [
                        Text(
                          recordingState.formatdDuration,
                          style: TextStyle(
                            fontSize: 14.0,
                            color: Theme.of(context).colorScheme.tertiary,
                          ),
                        ),
                        const SizedBox(width: 8.0),
                      ],
                    ),
                  if (recordingState.recorderStatus == 'record')
                    Container(
                      width: 80,
                      height: 30,
                      alignment: Alignment.center,
                      child: AudioWaveform(
                        isRecording: true,
                        color: appColors.waveformColor,
                      ),
                    ),
                ],
              )
            ],
          ),
        );
      },
    );
  }

  Future<void> startRecording() async {
    if (userPlanProvider.userPlan?.limited == true &&
        userPlanProvider.userPlan?.isPremium == false) {
      if (mounted) callPlan(context);
      return;
    }
    int todayDuration = await sintesyCrud.getTodayTotalDuration() ~/ 60;
    if (todayDuration > (userPlanProvider.userPlan?.minuteLimit ?? 15) &&
        !(userPlanProvider.userPlan?.isPremium ?? false)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Limite diário atingido. Aguarde até amanhã'),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TextButton(
                      onPressed: () {
                        callPlan(context);
                      },
                      child: const Text('Se torne PRO'),
                    ),
                  ],
                ),
              ],
            ),
            duration: const Duration(seconds: 5),
          ),
        );
      }
      return;
    }

    if (await permissionService.requestPermissions()) {
      await recordingService.startRecording();
      await handleTaskService.startService();

      _animationController.repeat(reverse: true);
      setState(() {});

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Gravação iniciada'),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.only(
              bottom: MediaQuery.of(context).size.height - 80,
              left: 20,
              right: 20,
            ),
          ),
        );
      }
    } else {
      // print('Permission denied');
    }
  }

  Future<SintesyModel?> stopRecording() async {
    SintesyModel? sintesy = await recordingService.stopRecording();
    await handleTaskService.stopService();
    if (_animationController.isAnimating || !_animationController.isDismissed) {
      _animationController.stop();
    }

    if (sintesy != null) {
      setState(() {
        _isPreviewMode = true;
        _recordedSintesy = sintesy;
      });
    } else {
      setState(() {});
    }

    return sintesy;
  }

  void onRecordButtonPressed() async {
    if (recordingState.recorderStatus == 'stop') {
      if (_isPreviewMode) {
        return;
      }
      await startRecording();
    } else {
      await stopRecording();
    }
  }

  Future<void> playPreviewAudio() async {
    if (_recordedSintesy == null || _recordedSintesy!.path == null) return;

    if (_isPlaying) {
      await _audioPlayer.pausePlayer();
      setState(() {
        _isPlaying = false;
      });
    } else {
      try {
        //printar o tamanho do arquivo
        print('Tamanho do arquivo: ${await File(_recordedSintesy!.path!).length()}');
        await _audioPlayer.startPlayer(
          fromURI: _recordedSintesy!.path!,
          whenFinished: () {
            setState(() {
              _isPlaying = false;
            });
          },
        );
        setState(() {
          _isPlaying = true;
        });
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erro ao reproduzir áudio: ${e.toString()}'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    }
  }

  void cancelRecording() async {
    if (_recordedSintesy != null) {
      if (_isPlaying) {
        await _audioPlayer.stopPlayer();
      }

      try {
        if (_recordedSintesy!.path != null) {
          final file = File(_recordedSintesy!.path!);
          if (await file.exists()) {
            await file.delete();
          }
        }
        await sintesyCrud.delete(_recordedSintesy!.uuid);
      } catch (e) {}

      setState(() {
        _isPreviewMode = false;
        _isPlaying = false;
        _recordedSintesy = null;
      });
    }
  }

  void sendRecording() async {
    if (_recordedSintesy == null) return;
    if (_isPlaying) {
      await _audioPlayer.stopPlayer();
    }

    final sintesy = _recordedSintesy!;

    setState(() {
      _isPreviewMode = false;
      _isPlaying = false;
      _recordedSintesy = null;
    });

    if (mounted) {
      bool hasInternet =
          Provider.of<ConnectionProvider>(context, listen: false).status;

      if (hasInternet) {
        if (mounted) {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => _FakeProgressDialog(
              title: 'Enviando gravação...',
              onComplete: () {},
            ),
          );
        }

        try {
          final result = await sintesySyncService.uploadLocalSintesy(sintesy);

          if (result == 403) {
            await sintesyCrud.delete(sintesy.uuid);
            if (mounted) Navigator.of(context).pop();
            if (mounted) callPlan(context);
            return;
          }

          if (mounted) Navigator.of(context).pop();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Gravação enviada com sucesso!'),
                duration: Duration(seconds: 2),
              ),
            );
          }
        } catch (e) {
          await sintesyCrud.delete(sintesy.uuid);

          if (mounted) Navigator.of(context).pop();

          if (e.toString().contains("403") ||
              e.toString().contains("Limite diário atingido")) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Limite diário atingido'),
                  duration: Duration(seconds: 2),
                ),
              );
            }
            if (mounted) callPlan(context);
          } else {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Erro ao enviar: ${e.toString()}'),
                  duration: const Duration(seconds: 3),
                ),
              );
            }
          }
        }
      } else {
        await sintesyCrud.add(sintesy);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Gravação salva localmente. Será enviada quando houver conexão.'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    }
  }

  Future<void> pauseRecording() async {
    _animationController.stop();
    setState(() {
      recordingService.pauseRecording();
    });
  }

  Future<void> resumeRecording() async {
    _animationController.repeat(reverse: true);
    setState(() {
      recordingService.resumeRecording();
    });
  }

  void onPauseButtonPressed() async {
    if (recordingState.recorderStatus == 'pause') {
      await resumeRecording();
    } else {
      await pauseRecording();
    }
  }
}
