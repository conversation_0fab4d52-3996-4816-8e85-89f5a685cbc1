import 'package:flutter/material.dart';

class CustomFormField extends StatelessWidget {
  final String label;
  final TextEditingController? controller; // Add this line
  final bool obscureText;
    final String? Function(String?)? validator; 
    final void Function(String)? onChanged;

  const CustomFormField({
    super.key,
    required this.label,
    this.controller, // Add this line
    this.obscureText = false,
    this.validator,
    this.onChanged
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller, // Add this line
      obscureText: obscureText,
      validator: validator, 
      decoration: InputDecoration(
        label: Text(
          label,
        ),
        labelStyle:  TextStyle(
          color: Theme.of(context).colorScheme.tertiary,
        ),
        border: const OutlineInputBorder(),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: Theme.of(context).primaryColor,
          ),
        ),
      ),
      onChanged: onChanged,
    );
  }
}