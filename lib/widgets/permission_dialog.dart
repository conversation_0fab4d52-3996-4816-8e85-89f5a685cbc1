import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

void _showPermissionDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: const Text('Permissão necessária'),
        content: const Text(
            'Para que o aplicativo funcione corretamente, é necessário conceder permissão de notificação. Por favor, habilite-a nas configurações.'),
        actions: <Widget>[
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () {
              openAppSettings();
            },
            child: const Text('Abrir Configurações'),
          ),
        ],
      );
    },
  );
}