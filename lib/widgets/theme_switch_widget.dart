import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';

/// Widget para alternar entre temas
/// Pode ser usado em configurações ou como um botão flutuante
class ThemeSwitchWidget extends StatelessWidget {
  final bool showLabel;
  final bool isIconButton;

  const ThemeSwitchWidget({
    super.key,
    this.showLabel = true,
    this.isIconButton = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        if (isIconButton) {
          return IconButton(
            icon: Icon(themeProvider.currentThemeIcon),
            onPressed: () => _showThemeDialog(context, themeProvider),
            tooltip: 'Alterar tema',
          );
        }

        return ListTile(
          leading: Icon(themeProvider.currentThemeIcon),
          title: const Text('Tema'),
          subtitle: showLabel ? Text(themeProvider.currentThemeName) : null,
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: () => _showThemeDialog(context, themeProvider),
        );
      },
    );
  }

  /// Mostra um diálogo para seleção de tema
  void _showThemeDialog(BuildContext context, ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Escolher Tema'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildThemeOption(
                context,
                themeProvider,
                ThemeMode.light,
                'Claro',
                Icons.light_mode,
              ),
              _buildThemeOption(
                context,
                themeProvider,
                ThemeMode.dark,
                'Escuro',
                Icons.dark_mode,
              ),
              _buildThemeOption(
                context,
                themeProvider,
                ThemeMode.system,
                'Sistema',
                Icons.brightness_auto,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancelar'),
            ),
          ],
        );
      },
    );
  }

  /// Constrói uma opção de tema no diálogo
  Widget _buildThemeOption(
    BuildContext context,
    ThemeProvider themeProvider,
    ThemeMode themeMode,
    String title,
    IconData icon,
  ) {
    final isSelected = themeProvider.themeMode == themeMode;

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? Theme.of(context).colorScheme.primary : null,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? Theme.of(context).colorScheme.primary : null,
          fontWeight: isSelected ? FontWeight.bold : null,
        ),
      ),
      trailing: isSelected
          ? Icon(
              Icons.check,
              color: Theme.of(context).colorScheme.primary,
            )
          : null,
      onTap: () {
        themeProvider.setThemeMode(themeMode);
        Navigator.of(context).pop();
      },
    );
  }
}

/// Widget simples para alternar tema com um switch
class ThemeToggleSwitch extends StatelessWidget {
  const ThemeToggleSwitch({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        // Para o switch, consideramos apenas claro/escuro
        final isDark = themeProvider.themeMode == ThemeMode.dark ||
            (themeProvider.themeMode == ThemeMode.system &&
                MediaQuery.of(context).platformBrightness == Brightness.dark);

        return SwitchListTile(
          title: const Text('Tema Escuro'),
          subtitle: Text(themeProvider.currentThemeName),
          value: isDark,
          secondary: Icon(themeProvider.currentThemeIcon),
          onChanged: (bool value) {
            if (value) {
              themeProvider.setDarkMode();
            } else {
              themeProvider.setLightMode();
            }
          },
        );
      },
    );
  }
}

/// Botão flutuante para alternar tema rapidamente
class ThemeFloatingActionButton extends StatelessWidget {
  const ThemeFloatingActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return FloatingActionButton(
          mini: true,
          onPressed: themeProvider.toggleTheme,
          tooltip: 'Alternar tema',
          child: Icon(themeProvider.currentThemeIcon),
        );
      },
    );
  }
}
