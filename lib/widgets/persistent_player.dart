import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'dart:async';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/providers/audio_player_provider.dart';
import 'package:sintesy_app/services/sintesy_client.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';

class PersistentPlayer extends StatefulWidget {
  final String? sintesyId;
  final String title;

  const PersistentPlayer({
    super.key,
    required this.sintesyId,
    required this.title,
  });

  @override
  State<PersistentPlayer> createState() => _PersistentPlayerState();
}

class _PersistentPlayerState extends State<PersistentPlayer> {
  final FlutterSoundPlayer _player = FlutterSoundPlayer();
  StreamSubscription? _playerSubscription;
  String _position = '00:00';
  String _duration = '00:00';
  bool _isLoading = false;
  late AudioState audioState;
  int _currentPartNumber = 0;
  final List<String> _partFilePaths = [];
  bool _isFetchingNextPart = false;
  bool _hasMoreParts = true;
  String? _currentAudioFilePath;
  Dio dio = Dio();

  @override
  void initState() {
    super.initState();
    audioState = Provider.of<AudioState>(context, listen: false);
    _initPlayer();
  }

  @override
  void didUpdateWidget(PersistentPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.sintesyId != oldWidget.sintesyId) {
      _resetPlayer();
      if (widget.sintesyId != null) {
        _initPlayer();
      } else {
        _stopAudio();
      }
    }
  }

  Future<void> _resetPlayer() async {
    await _stopAudio();
    _currentPartNumber = 0;
    _partFilePaths.clear();
    _hasMoreParts = true;
    _currentAudioFilePath = null;
  }

  Future<void> _initPlayer() async {
    await _player.openPlayer();
    await _player.setSubscriptionDuration(const Duration(milliseconds: 50));
    _setupProgressSubscription();

    if (widget.sintesyId != null) {
      _fetchNextPart();
    }
  }

  Future<void> _fetchNextPart() async {
    if (_player.isStopped) {
      setState(() => _isLoading = true);
    }
    if (!_hasMoreParts || _isFetchingNextPart) {
      return;
    }
    if (widget.sintesyId == null) return;
    _isFetchingNextPart = true;
    try {
      final sintesyAPI = SintesyAPI();
      final response = await sintesyAPI.getPodcastPart(
          widget.sintesyId!, _currentPartNumber);

      if (response.statusCode == 202 || response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final partUrl = data['url'];
        final tempDir = await getTemporaryDirectory();
        final filePath =
            '${tempDir.path}/part_${widget.sintesyId}_$_currentPartNumber.aac';
        await dio.download(partUrl, filePath);
        _partFilePaths.add(filePath);

        if (_currentAudioFilePath == null && _player.isStopped) {
          if (_partFilePaths.isNotEmpty) {
            _currentAudioFilePath = _partFilePaths.removeAt(0);
            _playCurrentAudio();
          }
        }
        _currentPartNumber++;
      } else if (response.statusCode == 404) {
        _hasMoreParts = false;
        if (_player.isStopped && _partFilePaths.isNotEmpty) {
          _currentAudioFilePath = _partFilePaths.removeAt(0);
          _playCurrentAudio();
        }
      } else {
        _hasMoreParts = false;
      }
    } catch (e) {
      _hasMoreParts = false;
    } finally {
      _isFetchingNextPart = false;
    }
    setState(() => _isLoading = false);
  }

  Future<void> _playCurrentAudio() async {
    if (_currentAudioFilePath == null) return;

    try {
      _setupProgressSubscription();
      setState(() => _isLoading = true);
      await _player.startPlayer(
        fromURI: _currentAudioFilePath,
        codec: Codec.aacMP4,
        whenFinished: () async {
          setState(() {
            _playerSubscription?.cancel();
          });
          _currentAudioFilePath = null;
          if (_partFilePaths.isNotEmpty) {
            _currentAudioFilePath = _partFilePaths.removeAt(0);
            _playCurrentAudio();
          } else if (_hasMoreParts) {
            _fetchNextPart();
          } else {
            audioState.hidePlayer();
          }
        },
      );
      audioState.setPlayer(_player);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _stopAudio() async {
    await _player.stopPlayer();
    audioState.hidePlayer();
    _player.closePlayer();
    _player.openPlayer();
    _partFilePaths.clear();
    _currentAudioFilePath = null;
    _currentPartNumber = 0;
    _hasMoreParts = true;
  }

  void _setupProgressSubscription() {
    _playerSubscription?.cancel();
    _playerSubscription = _player.onProgress?.listen((event) {
      final position = event.position;
      final duration = event.duration;

      setState(() {
        _position = _formatDuration(position);
        _duration = _formatDuration(duration);
      });

      if (duration > Duration.zero &&
          duration - position <= const Duration(seconds: 5)) {
        _fetchNextPart();
      }
    });
  }

  String _formatDuration(Duration duration) {
    return DateFormat('mm:ss').format(
      DateTime.fromMillisecondsSinceEpoch(
        duration.inMilliseconds,
        isUtc: true,
      ),
    );
  }

  Future<void> _playPause() async {
    if (_player.isStopped) {
      if (widget.sintesyId == null) return;
      if (_currentAudioFilePath != null) {
        _playCurrentAudio();
      } else if (_partFilePaths.isNotEmpty) {
        _currentAudioFilePath = _partFilePaths.removeAt(0);
        _playCurrentAudio();
      } else if (_hasMoreParts) {
        _fetchNextPart();
      }
    } else if (_player.isPlaying) {
      await _player.pausePlayer();
    } else {
      await _player.resumePlayer();
    }
  }

  @override
  void dispose() {
    _playerSubscription?.cancel();
    _player.closePlayer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 75,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    widget.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(
                        Icons.stop_circle,
                        color: Colors.black87,
                        size: 28,
                      ),
                      onPressed: _isLoading ? null : _stopAudio,
                    ),
                    IconButton(
                      icon: _isLoading
                          ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                              ),
                            )
                          : Icon(
                              _player.isPlaying
                                  ? Icons.pause_circle_filled
                                  : Icons.play_circle_filled,
                              color: Theme.of(context).primaryColor,
                              size: 32,
                            ),
                      onPressed: _isLoading ? null : _playPause,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
