import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/main.dart';
import 'package:sintesy_app/services/payment_client.dart';
import 'package:sintesy_app/services/user_plan_manager.dart';
import 'package:sintesy_app/src/user_plan_model.dart';
import 'package:sintesy_app/utils/subscription_process.dart';

Widget _buildFeatureItem(String feature) {
  return Row(
    children: [
      const Icon(Icons.check_circle, color: Colors.lightBlue, size: 20),
      const SizedBox(width: 8),
      Expanded(
        child: Text(
          feature,
          style: const TextStyle(fontSize: 14),
        ),
      ),
    ],
  );
}

class SubscriptionPopup extends StatefulWidget {
  final Map<String, dynamic> plan;
  const SubscriptionPopup({super.key, required this.plan});

  @override
  _SubscriptionPopupState createState() => _SubscriptionPopupState();
}

class _SubscriptionPopupState extends State<SubscriptionPopup> {
  bool _isLoading = false;
  bool _isValidatingCoupon = false;
  PaymentClient paymentClient = PaymentClient();

  final TextEditingController _couponController = TextEditingController();
  Map<String, dynamic>? _validatedCoupon;
  String? _couponError;
  bool _showCouponField = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _couponController.dispose();
    super.dispose();
  }

  Future<void> _validateCoupon() async {
    if (_couponController.text.trim().isEmpty) {
      setState(() {
        _couponError = 'Digite um código de cupom';
      });
      return;
    }

    setState(() {
      _isValidatingCoupon = true;
      _couponError = null;
    });

    try {
      final coupon = await paymentClient.validateCoupon(_couponController.text.trim());
      setState(() {
        _validatedCoupon = coupon;
        _isValidatingCoupon = false;
      });
    } catch (e) {
      setState(() {
        _couponError = e.toString().replaceFirst('Exception: ', '');
        _validatedCoupon = null;
        _isValidatingCoupon = false;
      });
    }
  }

  void _removeCoupon() {
    setState(() {
      _validatedCoupon = null;
      _couponError = null;
      _couponController.clear();
    });
  }

  double _calculateDiscountedPrice() {
    if (_validatedCoupon == null) return double.parse(widget.plan["amount"].toString());

    double originalPrice = double.parse(widget.plan["amount"].toString().replaceAll(',', '.'));
    String discountType = _validatedCoupon!['discount_type'];
    double discountValue = double.parse(_validatedCoupon!['discount_value'].toString());

    if (discountType == 'percentage') {
      return originalPrice * (1 - discountValue / 100);
    } else if (discountType == 'fixed') {
      return (originalPrice - discountValue).clamp(0.0, originalPrice);
    }

    return originalPrice;
  }

  String _formatPrice(double price) {
    return price.toStringAsFixed(2);
  }

  @override
  Widget build(BuildContext context) {
    UserPlanProvider userPlanProvider =
        Provider.of<UserPlanProvider>(context, listen: false);
    DateTime now = DateTime.now();
    DateTime firstChargeDate =
        now.add(Duration(days: widget.plan['trial_period_days']));
    String formattedFirstChargeDate =
        DateFormat('dd/MM/yyyy').format(firstChargeDate);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Destrave todo potencial',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 10),
          _buildFeatureItem('Sintesys ilimitadas'),
          _buildFeatureItem('Sintesys super rápidas'),
          _buildFeatureItem('Youtube sem limites'),
          _buildFeatureItem('Exportação PDF/DOCX'),
          _buildFeatureItem('Envie arquivos pelo WhatsApp'),
          const SizedBox(height: 20),
          Text(
            'Ao tocar em "Começar Grátis", você concorda que a assinatura será cobrada em $formattedFirstChargeDate no valor de ${widget.plan["currency"]}${_validatedCoupon != null ? _formatPrice(_calculateDiscountedPrice()) : widget.plan["amount"]}${_validatedCoupon != null && _validatedCoupon!['duration_in_months'] != null ? ' e após ${_validatedCoupon!['duration_in_months']} meses será cobrado o valor de ${widget.plan["currency"]}${widget.plan["amount"]}' : ''} e será renovada até que cancele. Para cancelar acesse sintesy.me.',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.2),
            ),
          ),
          const SizedBox(height: 10),
          // Coupon Section
          if (!_showCouponField && _validatedCoupon == null)
            TextButton.icon(
              onPressed: () {
                setState(() {
                  _showCouponField = true;
                });
              },
              icon: const Icon(Icons.local_offer, size: 18),
              label: const Text('Tenho um cupom de desconto'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.blue,
              ),
            ),

          if (_showCouponField && _validatedCoupon == null) ...[
            const SizedBox(height: 10),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _couponController,
                    decoration: InputDecoration(
                      hintText: 'Digite o código do cupom',
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      errorText: _couponError,
                    ),
                    textCapitalization: TextCapitalization.characters,
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isValidatingCoupon ? null : _validateCoupon,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: _isValidatingCoupon
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('Aplicar'),
                ),
              ],
            ),
            const SizedBox(height: 5),
            Row(
              children: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      _showCouponField = false;
                      _couponController.clear();
                      _couponError = null;
                    });
                  },
                  child: const Text('Cancelar'),
                ),
              ],
            ),
          ],

          if (_validatedCoupon != null) ...[
            const SizedBox(height: 10),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                border: Border.all(color: Colors.green.shade200),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green.shade600, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Cupom ${_validatedCoupon!['code']} aplicado! 🎉',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.green.shade700,
                          ),
                        ),
                        Text(
                          '${_validatedCoupon!['discount_display'] ?? 'Desconto aplicado'}${_validatedCoupon!['duration_in_months'] != null ? ' por ${_validatedCoupon!['duration_in_months']} meses' : ''}',
                          style: TextStyle(
                            color: Colors.green.shade600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: _removeCoupon,
                    icon: Icon(Icons.close, color: Colors.green.shade600, size: 18),
                    constraints: const BoxConstraints(),
                    padding: EdgeInsets.zero,
                  ),
                ],
              ),
            ),
          ],


          // Price Section
          if (_validatedCoupon != null) ...[
                        if (_validatedCoupon!['duration_in_months'] != null) ...[
              const SizedBox(height: 5),
              Text(
                'Desconto válido por ${_validatedCoupon!['duration_in_months']} meses',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                  color: Colors.orange.withOpacity(0.5),
                ),
              ),
            ],
            const SizedBox(height: 10),
            // Original price with strikethrough
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${widget.plan["currency"]} ${widget.plan["amount"]}',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                    decoration: TextDecoration.lineThrough,
                  ),
                ),
                Text(
                  '/mês',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.1),
                  ),
                ),
              ],
            ),
            // Discounted price
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${widget.plan["currency"]} ${_formatPrice(_calculateDiscountedPrice())}',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color.fromARGB(255, 138, 255, 142),
                  ),
                ),
                 Text(
                  '/mês',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                  ),
                ),
              ],
            ),
            // Duration notice if applicable

          ] else
            // Normal price display when no coupon
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${widget.plan["currency"]} ${widget.plan["amount"]}',
                  style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface),
                ),
                Text(
                  '/mês',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                  ),
                ),
              ],
            ),
          const SizedBox(height: 10),
          ElevatedButton(
            onPressed: _isLoading
                ? null
                : () async {
                    setState(() {
                      _isLoading = true;
                    });

                    final payment = await initPay(context, couponCode: _validatedCoupon != null ? _couponController.text.trim() : null);
                    if (payment) {
                      userPlanProvider.setUserPlan(UserPlan(
                          isPremium: true,
                          limitReachedDate: null,
                          limitMinutesTime: 500,
                          minuteLimit: 500,
                          limited: false,
                          whatsapp: "",
                          whatsappEnabled: true));

                      scaffoldMessengerKey.currentState?.showSnackBar(
                        const SnackBar(
                          backgroundColor: Colors.green,
                          content: Center(
                            child: Text(
                              'Agora você é PRO!',
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      );
                    } else {
                      scaffoldMessengerKey.currentState?.showSnackBar(
                        const SnackBar(
                          backgroundColor: Colors.black,
                          content: Text('Falha ao configurar pagamento.'),
                        ),
                      );
                    }

                    setState(() {
                      _isLoading = false;
                    });
                  },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black87,
              padding: const EdgeInsets.symmetric(horizontal: 60, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              side: const BorderSide(
                color: Colors.black12,
                width: 2,
              ),
            ),
            child: _isLoading
                ? const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.lightBlue),
                  )
                : const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.rocket_launch, color: Colors.white, size: 20),
                      SizedBox(width: 8),
                      Text(
                        'Começar Grátis',
                        style: TextStyle(fontSize: 18, color: Colors.white),
                      ),
                    ],
                  ),
          ),
          const SizedBox(height: 5),
          Text(
            (widget.plan['trial_period_days'] > 0)
                ? 'Primeiros ${widget.plan['trial_period_days']} dias gratis!'
                : '',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }
}

void showSubscriptionModal(BuildContext context, Map<String, dynamic> plan) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (BuildContext context) {
      return Wrap(
        children: [
          SubscriptionPopup(plan: plan),
        ],
      );
    },
  );
}
