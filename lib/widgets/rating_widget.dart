import 'package:flutter/material.dart';
import 'package:sintesy_app/services/rating_service.dart';

class SintesyRatingWidget extends StatefulWidget {
  final String sintesyUuid;
  final Function(bool success, String message) onRatingSubmitted;

  const SintesyRatingWidget({
    Key? key,
    required this.sintesyUuid,
    required this.onRatingSubmitted,
  }) : super(key: key);

  @override
  State<SintesyRatingWidget> createState() => _SintesyRatingWidgetState();
}

class _SintesyRatingWidgetState extends State<SintesyRatingWidget> {
  final RatingService _ratingService = RatingService();
  bool _isSubmitting = false;

  Future<void> _submitRating(RatingType rating) async {
    if (_isSubmitting) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final success = await _ratingService.rateSintesy(widget.sintesyUuid, rating);
      
      String message;
      if (success) {
        message = 'Obrigado pela sua avaliação!';
      } else {
        message = 'Não foi possível enviar sua avaliação. Tente novamente mais tarde.';
      }
      
      widget.onRatingSubmitted(success, message);
    } catch (e) {
      widget.onRatingSubmitted(false, 'Erro ao enviar avaliação: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.sintesyUuid.isEmpty) {
      return const SizedBox.shrink(); // Não exibe nada se o UUID estiver vazio
    }
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'O que achou desta síntese?',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [

              _buildRatingButton(
                label: '👎 Péssimo',
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                onTap: () => _submitRating(RatingType.bad),
              ),
                            _buildRatingButton(
                label: '🤩 Perfeito!',
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                onTap: () => _submitRating(RatingType.perfect),
              ),
              _buildRatingButton(
                label: '👍 Bom!',
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                onTap: () => _submitRating(RatingType.good),
              ),
            ],
          ),
          if (_isSubmitting) ...[
            const SizedBox(height: 16),
            const CircularProgressIndicator(),
          ],
        ],
      ),
    );
  }

  Widget _buildRatingButton({
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: _isSubmitting ? null : onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}