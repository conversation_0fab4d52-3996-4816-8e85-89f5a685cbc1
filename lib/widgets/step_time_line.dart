import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/providers/sintesy_state.dart';
import 'package:timelines/timelines.dart';
import '../themes/light_theme.dart';

class StepsTimeline extends StatelessWidget {
  const StepsTimeline({super.key});

  // Método para verificar quantas vezes o app foi aberto
  Future<int> _getSintesysLenght(BuildContext context) async {
    final sintesyState = Provider.of<SintesyState>(context, listen: false);
    await sintesyState.sintesysRefresh();
    return sintesyState.sintesys.where((s) => s.deleted != true).length;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<int>(
      future: _getSintesysLenght(context), // Espera pela contagem de aberturas
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const CircularProgressIndicator(); // Exibe um carregando enquanto aguarda a contagem
        }

        if (snapshot.hasData && snapshot.data! < 2) {
          // Exibe o StepsTimeline apenas nas duas primeiras vezes
          const List<String> steps = [
            "Grave insight,\naula,\ndiscurso",
            "Gere uma Sintesy",
            "Acesse o resumo\ne a transcrição!",
          ];

          return FixedTimeline.tileBuilder(
            builder: TimelineTileBuilder.connectedFromStyle(
              contentsAlign: ContentsAlign.alternating,
              oppositeContentsBuilder: (context, index) => const Padding(
                padding: EdgeInsets.all(8.0),
                child: Text(""),
              ),
              contentsBuilder: (context, index) => Card(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    steps[index],
                    style: TextStyle(color: Theme.of(context).colorScheme.tertiary.withOpacity(0.6)),
                  ),
                ),
              ),
              connectorStyleBuilder: (context, index) => ConnectorStyle.solidLine,
              indicatorStyleBuilder: (context, index) => IndicatorStyle.outlined,
              itemCount: steps.length,
            ),
          );
        } else {
          return const SizedBox.shrink(); // Se não for a primeira ou segunda vez, não exibe nada
        }
      },
    );
  }
}
