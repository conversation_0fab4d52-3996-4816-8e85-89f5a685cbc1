import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../themes/light_theme.dart';

class CustomBottomNavigationBar extends StatefulWidget {
  final Function onRecordTap;
  final Function onYoutubeTap;
  final Function onNoteTap;
  final Function onFileTap;
  final Function onMeetingTap;

  const CustomBottomNavigationBar({
    super.key,
    required this.onRecordTap,
    required this.onYoutubeTap,
    required this.onNoteTap,
    required this.onFileTap,
    required this.onMeetingTap,
  });

  @override
  State<CustomBottomNavigationBar> createState() => _CustomBottomNavigationBarState();
}

class _CustomBottomNavigationBarState extends State<CustomBottomNavigationBar>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final appColors = Theme.of(context).extension<AppColors>();
    final screenWidth = MediaQuery.of(context).size.width;

    return KeyboardVisibilityBuilder(
      builder: (context, isKeyboardVisible) {
        if (isKeyboardVisible) {
          return const SizedBox.shrink();
        }

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                appColors?.containerGradientStart ?? Theme.of(context).colorScheme.surface,
                (appColors?.containerGradientEnd ?? Theme.of(context).colorScheme.surface).withValues(alpha: 0.95),
              ],
            ),
            borderRadius: BorderRadius.circular(50),
            boxShadow: [
              BoxShadow(
                color: appColors?.shadowColor ?? Colors.black12,
                spreadRadius: 1,
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
            border: Border.all(
              color: (appColors?.borderColor ?? Colors.grey).withValues(alpha: 0.15),
              width: 1.0,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0, horizontal: 12.0),
            child: _buildExpandableToolbar(context, screenWidth),
          ),
        );
      },
    );
  }

  Widget _buildExpandableToolbar(BuildContext context, double screenWidth) {
    // Define breakpoints para diferentes tamanhos de tela
    if (screenWidth >= 400) {
      // Tela grande: mostra todos os botões em uma linha
      return _buildFullToolbar(context);
    } else {
      // Telas menores: usa toolbar expansível
      return AnimatedBuilder(
        animation: _expandAnimation,
        builder: (context, child) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Primeira linha - botões principais
              _buildMainRow(context, screenWidth),
              // Segunda linha - botões secundários (animada)
              if (_expandAnimation.value > 0)
                SizeTransition(
                  sizeFactor: _expandAnimation,
                  child: Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: _buildSecondaryRow(context, screenWidth),
                  ),
                ),
            ],
          );
        },
      );
    }
  }

  Widget _buildMainRow(BuildContext context, double screenWidth) {
    if (screenWidth >= 350) {
      // Tela média: mostra 3 botões principais + botão "MAIS"
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildToolbarButton(
            context: context,
            label: 'GRAVAÇÃO',
            icon: FontAwesomeIcons.microphone,
            onTap: () => widget.onRecordTap(),
          ),
          _buildVerticalDivider(context),
          _buildToolbarButton(
            context: context,
            label: 'YOUTUBE',
            icon: FontAwesomeIcons.youtube,
            onTap: () => widget.onYoutubeTap(),
          ),
          _buildVerticalDivider(context),
          _buildToolbarButton(
            context: context,
            label: 'REUNIÃO',
            icon: FontAwesomeIcons.video,
            onTap: () => widget.onMeetingTap(),
          ),
          _buildVerticalDivider(context),
          _buildExpandButton(context),
        ],
      );
    } else {
      // Tela pequena: mostra 2 botões principais + botão "MAIS"
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildToolbarButton(
            context: context,
            label: 'GRAVAÇÃO',
            icon: FontAwesomeIcons.microphone,
            onTap: () => widget.onRecordTap(),
          ),
          _buildVerticalDivider(context),
          _buildToolbarButton(
            context: context,
            label: 'YOUTUBE',
            icon: FontAwesomeIcons.youtube,
            onTap: () => widget.onYoutubeTap(),
          ),
          _buildVerticalDivider(context),
          _buildExpandButton(context),
        ],
      );
    }
  }

  Widget _buildSecondaryRow(BuildContext context, double screenWidth) {
    if (screenWidth >= 350) {
      // Tela média: mostra os 2 botões restantes
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildToolbarButton(
            context: context,
            label: 'ANOTAÇÃO',
            icon: FontAwesomeIcons.penToSquare,
            onTap: () => widget.onNoteTap(),
          ),
          _buildVerticalDivider(context),
          _buildToolbarButton(
            context: context,
            label: 'ARQUIVO',
            icon: FontAwesomeIcons.fileArrowUp,
            onTap: () => widget.onFileTap(),
          ),
          // Espaços vazios para manter alinhamento
          _buildVerticalDivider(context),
          const Expanded(child: SizedBox()),
          _buildVerticalDivider(context),
          const Expanded(child: SizedBox()),
        ],
      );
    } else {
      // Tela pequena: mostra os 3 botões restantes
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildToolbarButton(
            context: context,
            label: 'REUNIÃO',
            icon: FontAwesomeIcons.video,
            onTap: () => widget.onMeetingTap(),
          ),
          _buildVerticalDivider(context),
          _buildToolbarButton(
            context: context,
            label: 'ANOTAÇÃO',
            icon: FontAwesomeIcons.penToSquare,
            onTap: () => widget.onNoteTap(),
          ),
          _buildVerticalDivider(context),
          _buildToolbarButton(
            context: context,
            label: 'ARQUIVO',
            icon: FontAwesomeIcons.fileArrowUp,
            onTap: () => widget.onFileTap(),
          ),
        ],
      );
    }
  }

  Widget _buildFullToolbar(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildToolbarButton(
          context: context,
          label: 'GRAVAÇÃO',
          icon: FontAwesomeIcons.microphone,
          onTap: () => widget.onRecordTap(),
        ),
        _buildVerticalDivider(context),
        _buildToolbarButton(
          context: context,
          label: 'YOUTUBE',
          icon: FontAwesomeIcons.youtube,
          onTap: () => widget.onYoutubeTap(),
        ),
        _buildVerticalDivider(context),
        _buildToolbarButton(
          context: context,
          label: 'REUNIÃO',
          icon: FontAwesomeIcons.video,
          onTap: () => widget.onMeetingTap(),
        ),
        _buildVerticalDivider(context),
        _buildToolbarButton(
          context: context,
          label: 'ANOTAÇÃO',
          icon: FontAwesomeIcons.penToSquare,
          onTap: () => widget.onNoteTap(),
        ),
        _buildVerticalDivider(context),
        _buildToolbarButton(
          context: context,
          label: 'ARQUIVO',
          icon: FontAwesomeIcons.fileArrowUp,
          onTap: () => widget.onFileTap(),
        ),
      ],
    );
  }

  Widget _buildExpandButton(BuildContext context) {
    final Color iconColor = Theme.of(context).colorScheme.secondary;
    final Color textColor = Theme.of(context).colorScheme.tertiary;

    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: _toggleExpansion,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(5),
                  child: AnimatedRotation(
                    turns: _isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      _isExpanded ? FontAwesomeIcons.chevronUp : FontAwesomeIcons.chevronDown,
                      color: iconColor,
                      size: 18,
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _isExpanded ? 'MENOS' : 'MAIS',
                  style: TextStyle(
                    fontSize: 9,
                    color: textColor,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }



  Widget _buildToolbarButton({
    required BuildContext context,
    required String label,
    required IconData icon,
    required Function onTap,
  }) {
    final Color iconColor = Theme.of(context).colorScheme.secondary;
    final Color textColor = Theme.of(context).colorScheme.tertiary;

    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => onTap(),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(5),
                  child: Icon(
                    icon,
                    color: iconColor,
                    size: 18,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 9,
                    color: textColor,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVerticalDivider(BuildContext context) {
    final appColors = Theme.of(context).extension<AppColors>()!;

    return Container(
      height: 24,
      width: 1,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: appColors.dividerColor.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(5),
      ),
    );
  }
}
