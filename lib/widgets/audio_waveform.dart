import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/providers/recording_state.dart';

class AudioWaveform extends StatelessWidget {
  final bool isRecording;
  final Color color;

  const AudioWaveform({
    super.key,
    required this.isRecording,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<RecordingState>(
      builder: (context, recordingState, child) {
        return SizedBox(
          height: 30,
          width: 80,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(
              recordingState.amplitudeLevels.length,
              (index) =>
                  _buildWaveLine(index, recordingState.amplitudeLevels[index]),
            ),
          ),
        );
      },
    );
  }

  Widget _buildWaveLine(int index, double amplitude) {
    const double minHeight = 3.0;
    const double maxHeight = 28.0;
    double indexFactor = 1.0;
    if (index % 3 == 0) {
      indexFactor = 0.85;
    } else if (index % 2 == 0) {
      indexFactor = 0.95;
    }
    final double height =
        minHeight + (maxHeight - minHeight) * amplitude * indexFactor;
    Color barColor;
    if (amplitude > 0.6) {
      barColor = color;
    } else if (amplitude > 0.3) {
      barColor = color.withOpacity(0.8);
    } else {
      barColor = color.withOpacity(0.6);
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 80),
      width: 2.5,
      height: height,
      decoration: BoxDecoration(
        color: barColor,
        borderRadius: BorderRadius.circular(20),
      ),
    );
  }
}
