import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/models/sintesy_model.dart';
import 'package:sintesy_app/providers/sintesy_state.dart';
import 'package:sintesy_app/providers/youtube_state.dart';
import 'package:sintesy_app/services/sintesy_local_crud.dart';
import 'package:sintesy_app/services/user_plan_manager.dart';
import 'package:sintesy_app/services/youtube_client.dart';
import 'package:sintesy_app/services/youtube_service.dart';
import 'package:sintesy_app/utils/call_plan.dart';
import 'package:youtube_explode_dart/youtube_explode_dart.dart';

class YouTubeLinkInput extends StatefulWidget {
  final String? initialLink;

  const YouTubeLinkInput({super.key, this.initialLink});

  @override
  _YouTubeLinkInputState createState() => _YouTubeLinkInputState();
}

class _YouTubeLinkInputState extends State<YouTubeLinkInput> {
  final TextEditingController controller = TextEditingController();
  final YoutubeExplode yt = YoutubeExplode();
  bool isProcessing = false;
  String? videoTitle;
  String? videoImage;
  String link = '';
  late YouTubeState youtubeState;
  final SintesyState sintesyState = SintesyState();
  late UserPlanProvider userPlanProvider;
  final SintesyCrud sintesyCrud = SintesyCrud();

  @override
  void initState() {
    super.initState();
    controller.addListener(_onTextChanged);
    youtubeState = Provider.of<YouTubeState>(context, listen: false);
    userPlanProvider = Provider.of<UserPlanProvider>(context, listen: false);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      youtubeState.setButtonVisible(false);

      if (widget.initialLink != null && widget.initialLink!.isNotEmpty) {
        controller.text = widget.initialLink!;
        _onTextChanged();
      }
    });
  }

  @override
  void dispose() {
    controller.removeListener(_onTextChanged);
    controller.dispose();
    super.dispose();
  }

  void _onTextChanged() async {
    if (controller.text.isNotEmpty && controller.text != '') {
      link = controller.text;
      controller.text = "";
      setState(() {});
      if (!link.contains("youtube.com") && !link.contains("youtu.be")) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Esse link não é do youtube'),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.only(
              bottom: MediaQuery.of(context).size.height - 80,
              left: 20,
              right: 20,
            ),
          ),
        );
        return;
      }
      setState(() {
        isProcessing = true;
      });

      try {
        if ((await sintesyCrud.getTodayTotalDuration()) >
                (userPlanProvider.userPlan?.minuteLimit ?? 15) &&
            !(userPlanProvider.userPlan?.isPremium ?? false)) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Limite diário atingido. Aguarde até amanhã'),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TextButton(
                        onPressed: () {
                          callPlan(context);
                        },
                        child: const Text('Se torne PRO'),
                      ),
                    ],
                  ),
                ],
              ),
              duration: const Duration(seconds: 5),
            ),
          );
          return;
        }
        final downloadService = AudioDownloadService(yt);
        final video = await downloadService.getVideo(link);
        if (video.duration != null &&
            video.duration! >
                Duration(
                    minutes: (userPlanProvider.userPlan?.minuteLimit ?? 15)) &&
            !(userPlanProvider.userPlan?.isPremium ?? false)) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Vídeo muito longo para o plano atual'),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton(
                        onPressed: () {
                          callPlan(context);
                        },
                        child: const Text('Se torne PRO'),
                      ),
                    ],
                  ),
                ],
              ),
              duration: const Duration(seconds: 5),
            ),
          );
          return;
        }
        setState(() {
          videoTitle = video.title;
          videoImage = video.thumbnails.highResUrl;
          youtubeState.setButtonVisible(true);
        });
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Erro ao processar o link')),
        );
      } finally {
        setState(() {
          isProcessing = false;
        });
      }
    } else {
      setState(() {
        youtubeState.setButtonVisible(false);
        videoTitle = null;
        videoImage = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 240,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: Theme.of(context).colorScheme.tertiary,
          width: 1.0,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const SizedBox(width: 10),
              Expanded(
                child: isProcessing
                    ? const Text(
                        'Processando vídeo...',
                        style: TextStyle(color: Colors.grey),
                      )
                    : TextField(
                        controller: controller,
                        decoration: const InputDecoration(
                          icon: Icon(
                            FontAwesomeIcons.youtube,
                            color: Colors.red,
                          ),
                          hintText: 'Cole link do YouTube',
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide.none,
                          ),
                          border: OutlineInputBorder(
                            borderSide: BorderSide.none,
                          ),
                        ),
                      ),
              ),
            ],
          ),
          if (videoTitle != null && videoImage != null) ...[
            const SizedBox(height: 10),
            Image.network(videoImage!),
            Text(videoTitle!,
                style:
                    const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center),
          ],
          Consumer<YouTubeState>(builder: (context, youtubeState, child) {
            return youtubeState.isButtonVisible
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white),
                        onPressed: isProcessing ? null : downloadVideo,
                        child: const Row(
                          children: [
                            Text(
                              'Gerar Sintesy',
                              style: TextStyle(fontSize: 15),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.cancel_outlined),
                        color: Colors.grey,
                        onPressed: reset,
                      ),
                    ],
                  )
                : const SizedBox();
          })
        ],
      ),
    );
  }

  Future<void> downloadVideo() async {
    if (userPlanProvider.userPlan?.limited == true &&
        userPlanProvider.userPlan?.isPremium == false) {
      callPlan(context);
      return;
    }
    final youtubeApi = YoutubeAPI();
    final sintesy = await _saveSintesyLocal(videoTitle!);

    try {
      final response =
          await youtubeApi.createFromYoutube(link, videoTitle, sintesy.uuid);
      if (!mounted) return;
      if (response.statusCode == 403) {
        final SintesyCrud sintesyCrud = SintesyCrud();
        await sintesyCrud.delete(sintesy.uuid);
        if (mounted) {
          callPlan(context);
        }
        return;
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text("✨Criando Sintesy"),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.only(
              bottom: MediaQuery.of(context).size.height - 80,
              left: 20,
              right: 20,
            ),
          ),
        );
      }

      sintesyState.sintesysRefresh();

      if (mounted) {
        setState(() {
          reset();
        });
      }
    } catch (e) {
      final SintesyCrud sintesyCrud = SintesyCrud();
      await sintesyCrud.delete(sintesy.uuid);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text("Erro ao criar Sintesy"),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.only(
              bottom: MediaQuery.of(context).size.height - 80,
              left: 20,
              right: 20,
            ),
          ),
        );
      }
    }
  }

  void reset() {
    youtubeState.setButtonVisible(false);
    videoImage = null;
    videoTitle = null;
    controller.text = "";
    link = '';
    isProcessing = false;
    setState(() {});
  }

  Future<SintesyModel> _saveSintesyLocal(String name) async {
    final SintesyCrud sintesyCrud = SintesyCrud();
    SintesyModel sintesy =
        SintesyModel(name: name, createdDate: DateTime.now());
    await sintesyCrud.add(sintesy);
    return sintesy;
  }
}
