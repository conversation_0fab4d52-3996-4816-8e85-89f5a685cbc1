import 'dart:math' show min;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:dio/dio.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sintesy_app/services/base_api.dart';
import 'package:sintesy_app/services/user_plan_manager.dart';
import 'package:sintesy_app/src/user_plan_model.dart';

class WhatsAppDialog extends StatefulWidget {
  const WhatsAppDialog({super.key});

  @override
  State<WhatsAppDialog> createState() => _WhatsAppDialogState();
}

class BrazilianPhoneInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final digitsOnly = newValue.text.replaceAll(RegExp(r'\D'), '');

    if (digitsOnly.isEmpty) {
      return newValue.copyWith(text: '');
    }

    String formattedValue = '';

    if (digitsOnly.length >= 2) {
      formattedValue = '(${digitsOnly.substring(0, 2)}';
    } else {
      formattedValue = '($digitsOnly';
    }

    if (digitsOnly.length > 2) {
      formattedValue += ') ';

      if (digitsOnly.length >= 3) {
        formattedValue += digitsOnly.substring(2, 3);
      }

      if (digitsOnly.length >= 7) {
        formattedValue += ' ${digitsOnly.substring(3, 7)}';
      } else if (digitsOnly.length > 3) {
        formattedValue += ' ${digitsOnly.substring(3)}';
      }

      if (digitsOnly.length > 7) {
        formattedValue +=
            '-${digitsOnly.substring(7, min(11, digitsOnly.length))}';
      }
    }

    return TextEditingValue(
      text: formattedValue,
      selection: TextSelection.collapsed(offset: formattedValue.length),
    );
  }
}

class _WhatsAppDialogState extends State<WhatsAppDialog> {
  final TextEditingController _phoneController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  bool _whatsappEnabled = true;
  bool _isPhoneValid = false;
  bool _hasExistingNumber = false;

  @override
  void initState() {
    super.initState();
    _phoneController.addListener(_validatePhone);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadExistingWhatsAppNumber();
    });
  }

  void _loadExistingWhatsAppNumber() {
    final userPlanProvider =
        Provider.of<UserPlanProvider>(context, listen: false);
    final userPlan = userPlanProvider.userPlan;

    if (userPlan != null &&
        userPlan.whatsapp != null &&
        userPlan.whatsapp!.isNotEmpty) {
      setState(() {
        _hasExistingNumber = true;
        final digitsOnly = userPlan.whatsapp!;
        if (digitsOnly.length >= 11) {
          final formattedNumber =
              '(${digitsOnly.substring(0, 2)}) ${digitsOnly.substring(2, 3)} ${digitsOnly.substring(3, 7)}-${digitsOnly.substring(7, 11)}';
          _phoneController.text = formattedNumber;
        } else {
          _phoneController.text = digitsOnly;
        }
        _whatsappEnabled = userPlan.whatsappEnabled ?? true;
      });
    }
  }

  @override
  void dispose() {
    _phoneController.removeListener(_validatePhone);
    _phoneController.dispose();
    super.dispose();
  }

  void _validatePhone() {
    final phoneText = _phoneController.text;
    final digitsOnly = phoneText.replaceAll(RegExp(r'\D'), '');
    setState(() {
      _isPhoneValid = digitsOnly.length == 11;
      if (_isPhoneValid) {
        _errorMessage = null;
      }
    });
  }

  Future<bool> _updateWhatsApp(String whatsappNumber) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        if (mounted) {
          setState(() {
            _errorMessage =
                'Erro de autenticação. Por favor, faça login novamente.';
            _isLoading = false;
          });
        }
        return false;
      }

      final url = "${BaseAPI.base}/user";
      final headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': 'Token $token',
      };

      final digitsOnly = whatsappNumber.replaceAll(RegExp(r'\D'), '');

      final formData = FormData.fromMap({
        'whatsapp': digitsOnly,
        'whatsapp_enabled': _whatsappEnabled.toString(),
      });

      final dio = Dio();
      final response = await dio.patch(
        url,
        options: Options(headers: headers),
        data: formData,
      );

      if (!mounted) return false;

      if (response.statusCode == 200) {
        final userPlanProvider =
            Provider.of<UserPlanProvider>(context, listen: false);
        final currentPlan = userPlanProvider.userPlan;

        if (currentPlan != null) {
          final updatedPlan = UserPlan(
            isPremium: currentPlan.isPremium,
            limitReachedDate: currentPlan.limitReachedDate,
            limitMinutesTime: currentPlan.limitMinutesTime,
            minuteLimit: currentPlan.minuteLimit,
            limited: currentPlan.limited,
            whatsapp: digitsOnly,
            whatsappEnabled: _whatsappEnabled,
          );

          userPlanProvider.setUserPlan(updatedPlan);
        }

        return true;
      } else {
        setState(() {
          _errorMessage = 'Erro ao atualizar WhatsApp: ${response.statusCode}';
          _isLoading = false;
        });
        return false;
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Erro ao atualizar WhatsApp: $e';
          _isLoading = false;
        });
      }
      return false;
    }
  }

  void _handleSave() async {
    final phoneNumber = _phoneController.text.trim();

    if (!_isPhoneValid) {
      setState(() {
        _errorMessage =
            'Por favor, digite um número de WhatsApp válido com 11 dígitos';
      });
      return;
    }

    final success = await _updateWhatsApp(phoneNumber);

    if (success && mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: _hasExistingNumber,
      child: AlertDialog(
        title: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FaIcon(
              FontAwesomeIcons.whatsapp,
              color: Color(0xFF25D366),
              size: 28,
            ),
            SizedBox(width: 10),
            Text(
              'Integração WhatsApp',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Coloque seu DDD + número!',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'BR +55',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: _phoneController,
                    keyboardType: TextInputType.phone,
                    inputFormatters: [
                      BrazilianPhoneInputFormatter(),
                    ],
                    decoration: InputDecoration(
                      hintText: '(99) 9 9999-9999',
                      border: const OutlineInputBorder(),
                      suffixIcon: _isPhoneValid
                          ? const Icon(Icons.check_circle, color: Colors.green)
                          : null,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Expanded(
                  child: Text(
                    'Habilitar integração com WhatsApp',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.black54,
                    ),
                  ),
                ),
                Transform.scale(
                  scale: 0.8,
                  child: Switch.adaptive(
                    value: _whatsappEnabled,
                    activeColor: const Color(0xFF25D366),
                    onChanged: (value) {
                      setState(() {
                        _whatsappEnabled = value;
                      });
                    },
                  ),
                ),
              ],
            ),
            if (_errorMessage != null)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  _errorMessage!,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
          ],
        ),
        actions: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isLoading || !_isPhoneValid ? null : _handleSave,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                disabledBackgroundColor: Colors.grey.shade300,
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(width: 8),
                        Text(
                          'Salvar',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
