import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/models/sintesy_model.dart';
import 'package:sintesy_app/pages/sintesy_viewer.dart';
import 'package:sintesy_app/providers/connection_checker.dart';
import 'package:sintesy_app/providers/sintesy_state.dart';
import 'package:sintesy_app/services/sintesy_sync_service.dart';
import '../themes/light_theme.dart';

class _ProgressPercentage extends StatefulWidget {
  final SintesyModel recording;

  const _ProgressPercentage({required this.recording});

  @override
  State<_ProgressPercentage> createState() => _ProgressPercentageState();
}

class _ProgressPercentageState extends State<_ProgressPercentage>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  double _currentPercentage = 0.0;
  double _targetPercentage = 0.0;
  String _displayPercentage = "0%";
  Timer? _incrementTimer;
  bool _hasReachedTarget = false;
  bool _showCheckmark = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Start with initial percentage based on status
    _updatePercentageFromStatus();

    // Start the incremental timer
    _startIncrementalProgress();
  }

  @override
  void didUpdateWidget(_ProgressPercentage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.recording.status != widget.recording.status) {
      _updatePercentageFromStatus();
    }
  }

  void _updatePercentageFromStatus() {
    final status = widget.recording.status;
    double newTarget;

    if (status == null) {
      newTarget = 79.0;
    } else {
      switch (status) {
        case 'processing':
          newTarget = 83.0;
          break;
        case 'transcribing':
          newTarget = 89.0;
          break;
        case 'generate_sintesy':
          newTarget = 99.0;
          break;
        case 'done':
          newTarget = 100.0;
          break;
        default:
          newTarget = 0.0;
      }
    }

    if (_targetPercentage != newTarget) {
      if (_targetPercentage == 0.0 && _currentPercentage == 0.0) {
        _currentPercentage = 0.0;
        _displayPercentage = "0%";
      }

      _targetPercentage = newTarget;
      _hasReachedTarget = false;
      _showCheckmark = false;

      if (_incrementTimer == null) {
        _startIncrementalProgress();
      }
    }
  }

  void _startIncrementalProgress() {
    _incrementTimer?.cancel();
    _incrementTimer =
        Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (_currentPercentage < _targetPercentage) {
        setState(() {
          if (_targetPercentage - _currentPercentage < 1) {
            _currentPercentage = _targetPercentage;
          } else {
            _currentPercentage += 1;
          }
          _displayPercentage = "${_currentPercentage.round()}%";
        });
      } else if (!_hasReachedTarget &&
          _currentPercentage >= _targetPercentage) {
        _hasReachedTarget = true;

        if (_targetPercentage >= 100) {
          Future.delayed(const Duration(seconds: 1), () {
            if (mounted) {
              setState(() {});
            }
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _incrementTimer?.cancel();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final appColors = Theme.of(context).extension<AppColors>()!;

    if (_hasReachedTarget && _currentPercentage >= 100.0 && _showCheckmark) {
      return Icon(
        Icons.check_circle,
        color: appColors.successColor,
        size: 24,
      );
    }

    if (_hasReachedTarget && _currentPercentage >= 100.0 && !_showCheckmark) {
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _showCheckmark = true;
          });
        }
      });
    }

    return Center(
      child: Container(
        padding: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: appColors.progressIndicator.withOpacity(0.1),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Text(
          _displayPercentage,
          style: TextStyle(
            color: appColors.progressIndicator,
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
      ),
    );
  }
}

class _RecordingIndicator extends StatefulWidget {
  @override
  State<_RecordingIndicator> createState() => _RecordingIndicatorState();
}

class _RecordingIndicatorState extends State<_RecordingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Opacity(
          opacity: 0.9,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                FontAwesomeIcons.video,
                size: 18,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 4),
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(_animation.value),
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class ListSintesy extends StatelessWidget {
  final String searchQuery;

  const ListSintesy({super.key, this.searchQuery = ''});

  @override
  Widget build(BuildContext context) {
    return Consumer2<SintesyState, SintesySyncService>(
      builder: (context, sintesyState, syncService, child) {
        List<SintesyModel> sintesys = sintesyState.sintesys;

        List<SintesyModel> activeSintesys =
            sintesys.where((sintesy) => sintesy.deleted != true).toList();

        List<SintesyModel> filteredSintesys = activeSintesys.where((sintesy) {
          final nameMatch =
              sintesy.name.toLowerCase().contains(searchQuery.toLowerCase());
          final summaryMatch = sintesy.summary
                  ?.toLowerCase()
                  .contains(searchQuery.toLowerCase()) ??
              false;
          final textMatch =
              sintesy.text?.toLowerCase().contains(searchQuery.toLowerCase()) ??
                  false;
          return nameMatch || summaryMatch || textMatch;
        }).toList();

        if (filteredSintesys.isEmpty && searchQuery.isNotEmpty) {
          return const Center(
              child: Text('Nenhuma sintesy encontrada para sua pesquisa'));
        } else if (activeSintesys.isEmpty && searchQuery.isEmpty) {
          return const Center(child: Text('Nenhuma sintesy encontrada'));
        } else {
          return _ListSintesyContent(
              sintesys: filteredSintesys, searchQuery: searchQuery);
        }
      },
    );
  }
}

class _ListSintesyContent extends StatefulWidget {
  final List<SintesyModel> sintesys;
  final String searchQuery;
  const _ListSintesyContent({required this.sintesys, this.searchQuery = ''});

  @override
  _ListSintesyContentState createState() => _ListSintesyContentState();
}

class _ListSintesyContentState extends State<_ListSintesyContent> {
  final List<String> _selectedIds = [];
  bool _isMultiSelecting = false;

  void _toggleSelection(String id) {
    setState(() {
      if (_selectedIds.contains(id)) {
        _selectedIds.remove(id);
      } else {
        _selectedIds.add(id);
      }
      _isMultiSelecting = _selectedIds.isNotEmpty;
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedIds.clear();
      _isMultiSelecting = false;
    });
  }

  Future<void> _deleteSelectedItems() async {
    if (_selectedIds.isEmpty) return;

    final syncService = Provider.of<SintesySyncService>(context, listen: false);

    try {
      for (String id in _selectedIds) {
        await syncService.deleteSintesy(context, id);
      }
      final appColors = Theme.of(context).extension<AppColors>()!;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          backgroundColor: appColors.successColor,
          content: const Text('Sintesys deletadas com sucesso!'),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.only(
            bottom: 80,
            left: 20,
            right: 20,
          ),
        ),
      );
    } catch (e) {
      final appColors = Theme.of(context).extension<AppColors>()!;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          backgroundColor: Theme.of(context).colorScheme.error,
          content: Text('Erro ao deletar sintesys: ${e.toString()}'),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          margin: EdgeInsets.only(
            bottom: MediaQuery.of(context).size.height - 80,
            left: 20,
            right: 20,
          ),
        ),
      );
    } finally {
      _clearSelection();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SintesyState>(
      builder: (context, sintesyState, child) {
        List<SintesyModel> sintesys = widget.sintesys;
        bool hasConnection = Provider.of<ConnectionProvider>(context).status;
        final searchQuery = widget.searchQuery;

        sintesys.sort((a, b) => b.createdDate.compareTo(a.createdDate));
        return Scaffold(
          appBar: _isMultiSelecting
              ? AppBar(
                  leading: IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: _clearSelection,
                  ),
                  title: Text('${_selectedIds.length} selecionados'),
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: _deleteSelectedItems,
                    ),
                  ],
                )
              : null,
          body: ListView.builder(
            itemCount:
                sintesys.length > 5 ? sintesys.length + 1 : sintesys.length,
            physics: const BouncingScrollPhysics(),
            padding: const EdgeInsets.all(3.0),
            cacheExtent: 2000,
            itemBuilder: (context, index) {
              if (sintesys.length > 5 && index == sintesys.length) {
                return const SizedBox(height: 80);
              }

              final recording = sintesys[index];
              final isSelected = _selectedIds.contains(recording.uuid);
              final nameMatch = searchQuery.isNotEmpty &&
                  recording.name
                      .toLowerCase()
                      .contains(searchQuery.toLowerCase());
              final summaryMatch = searchQuery.isNotEmpty &&
                  (recording.summary
                          ?.toLowerCase()
                          .contains(searchQuery.toLowerCase()) ??
                      false);
              final textMatch = searchQuery.isNotEmpty &&
                  (recording.text
                          ?.toLowerCase()
                          .contains(searchQuery.toLowerCase()) ??
                      false);

              return ListTile(
                selected: isSelected,
                onLongPress: () {
                  if (!_isMultiSelecting) {
                    _toggleSelection(recording.uuid);
                  }
                },

                title: nameMatch
                    ? _buildHighlightedTitle(recording.name, searchQuery)
                    : Row(
                        children: [
                          _buildLeadingIcon(recording, hasConnection),
                          const SizedBox(width: 10),
                          Expanded(
                            child: Text(
                              recording.name,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                  fontSize: 17, fontWeight: FontWeight.bold),
                            ),
                          ),
                        ],
                      ),
                subtitle: recording.summary != null &&
                        recording.summary!.isNotEmpty
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          summaryMatch || textMatch
                              ? _buildHighlightedSummarySnippet(
                                  recording.text ?? '', searchQuery)
                              : _buildSummarySnippet(context, recording.text ?? ''),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                DateFormat('dd/MM/yyyy')
                                    .format(recording.createdDate),
                                style: TextStyle(
                                    fontSize: 12, color: Theme.of(context).extension<AppColors>()!.subtitleText),
                              ),
                              Row(
                                children: [
                                  const SizedBox(width: 5),
                                  if (recording.mindmap != null)
                                    Icon(
                                      Icons.account_tree,
                                      size: 16,
                                      color: Theme.of(context).colorScheme.tertiary,
                                    ),
                                  if (recording.roadmap != null &&
                                      recording.roadmap != "")
                                    Icon(
                                      Icons.format_list_bulleted,
                                      size: 16,
                                      color: Theme.of(context).colorScheme.tertiary,
                                    ),
                                  if (recording.checklist != null &&
                                      recording.checklist != "")
                                    Icon(
                                      Icons.checklist,
                                      size: 16,
                                      color: Theme.of(context).colorScheme.tertiary,
                                    ),
                                  if (recording.summarySpeechUrl != null &&
                                      recording.summarySpeechUrl != "")
                                    Icon(
                                      Icons.voice_chat,
                                      size: 16,
                                      color: Theme.of(context).colorScheme.tertiary,
                                    ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      )
                    : _buildDefaultSubtitle(recording),
                //trailing: _buildTrailingActions(recording, context),
                onTap: () {
                  if (_isMultiSelecting) {
                    _toggleSelection(recording.uuid);
                    return;
                  }
                  if (recording.summary != null && recording.summary != "") {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) =>
                                SintesyViewer(sintesy: recording)));
                  } else if (!hasConnection) {
                    final appColors = Theme.of(context).extension<AppColors>()!;
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        backgroundColor: appColors.warningColor,
                        content: const Text('Sem conexão com a internet'),
                        duration: const Duration(seconds: 1),
                        behavior: SnackBarBehavior.floating,
                        margin: const EdgeInsets.only(
                          bottom: 80,
                          left: 20,
                          right: 20,
                        ),
                      ),
                    );
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        content: const Text('Criando Sintesy..'),
                        duration: const Duration(seconds: 2),
                        behavior: SnackBarBehavior.floating,
                        margin: const EdgeInsets.only(
                          bottom: 80,
                          left: 20,
                          right: 20,
                        ),
                      ),
                    );
                  }
                },
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildLeadingIcon(SintesyModel recording, bool hasConnection) {
    if (recording.localStatus == 'failed_send') {
      return Opacity(
        opacity: 0.9,
        child: Icon(Icons.error_outline, size: 30, color: Theme.of(context).colorScheme.error),
      );
    }

    // Verifica se é uma sintesy de reunião e tem status específico
    if (recording.attendeeBot != null) {
      return _buildMeetingStatusIcon(recording);
    }

    if (recording.summary == null || recording.summary == '') {
      return hasConnection
          ? Opacity(
              opacity: 0.9,
              child: SizedBox(
                width: 30,
                height: 30,
                child: _ProgressPercentage(recording: recording),
              ),
            )
          : const Opacity(
              opacity: 0.9,
              child: Icon(Icons.cloud_upload_outlined, size: 30),
            );
    }

    if (recording.source != null) {
      if (recording.source == 'youtube') {
        return Opacity(
          opacity: 0.9,
          child: Icon(FontAwesomeIcons.youtube, size: 18, color: Theme.of(context).colorScheme.error),
        );
      } else if (recording.source == 'bucket' || recording.source == 'audio') {
        return Opacity(
          opacity: 0.7,
          child: Icon(FontAwesomeIcons.microphone,
              size: 20, color: Theme.of(context).colorScheme.primary),
        );
      } else if (recording.source == 'text') {
        return Opacity(
          opacity: 0.9,
          child:
              Icon(FontAwesomeIcons.penToSquare, size: 18, color: Theme.of(context).colorScheme.tertiary),
        );
      } else if (recording.source == 'meet' || recording.source == 'teams') {
        return Opacity(
          opacity: 0.9,
          child: Icon(FontAwesomeIcons.video, size: 18, color: Theme.of(context).colorScheme.primary),
        );
      }
    }
    return Opacity(
      opacity: 0.9,
      child: Icon(FontAwesomeIcons.upload, size: 18, color: Theme.of(context).colorScheme.tertiary),
    );
  }

  Widget _buildMeetingStatusIcon(SintesyModel recording) {
    final botStatus = recording.attendeeBot?.botStatus;

    switch (botStatus) {
      case 'awaiting_approval':
        return Opacity(
          opacity: 0.9,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                FontAwesomeIcons.video,
                size: 18,
                color: Colors.orange,
              ),
              const SizedBox(width: 4),
              Icon(
                Icons.access_time,
                size: 16,
                color: Colors.orange,
              ),
            ],
          ),
        );
      case 'joined_recording':
        return _RecordingIndicator();
      default:
        return Opacity(
          opacity: 0.9,
          child: Icon(
            FontAwesomeIcons.video,
            size: 18,
            color: Theme.of(context).colorScheme.primary,
          ),
        );
    }
  }

  Widget _buildDefaultSubtitle(SintesyModel recording) {
    // Se é uma reunião, mostra o status específico
    if (recording.attendeeBot != null) {
      return _buildMeetingSubtitle(recording);
    }

    // Subtitle padrão para outras sintesys
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        recording.duration != null
            ? Text(
                _formatDuration(recording.duration!),
                style: TextStyle(
                    fontSize: 12, color: Theme.of(context).extension<AppColors>()!.subtitleText),
              )
            : const SizedBox(),
        Text(
          DateFormat('dd/MM/yyyy HH:mm')
              .format(recording.createdDate),
          style: TextStyle(
              fontSize: 12, color: Theme.of(context).extension<AppColors>()!.subtitleText),
        ),
      ],
    );
  }

  Widget _buildMeetingSubtitle(SintesyModel recording) {
    final botStatus = recording.attendeeBot?.botStatus;
    String statusText = '';
    Color statusColor = Theme.of(context).extension<AppColors>()!.subtitleText;

    switch (botStatus) {
      case 'awaiting_approval':
        statusText = 'Entrando na sala...';
        statusColor = Colors.orange;
        break;
      case 'joined_recording':
        statusText = 'Gravando reunião';
        statusColor = Colors.red;
        break;
      case 'post_processing':
        statusText = 'Processando gravação...';
        statusColor = Colors.blue;
        break;
      case 'ended':
        statusText = 'Gravação finalizada';
        statusColor = Colors.green;
        break;
      default:
        statusText = 'Reunião';
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          statusText,
          style: TextStyle(
            fontSize: 12,
            color: statusColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          DateFormat('dd/MM/yyyy HH:mm')
              .format(recording.createdDate),
          style: TextStyle(
              fontSize: 12, color: Theme.of(context).extension<AppColors>()!.subtitleText),
        ),
      ],
    );
  }

  Widget _buildHighlightedTitle(String text, String query) {
    final lowerText = text.toLowerCase();
    final lowerQuery = query.toLowerCase();
    final matchIndex = lowerText.indexOf(lowerQuery);

    if (matchIndex == -1) {
      return Text(text);
    }

    final beforeMatch = text.substring(0, matchIndex);
    final match = text.substring(matchIndex, matchIndex + query.length);
    final afterMatch = text.substring(matchIndex + query.length);

    return RichText(
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      text: TextSpan(
        style: TextStyle(fontSize: 16, color: Theme.of(context).colorScheme.onSurface),
        children: <TextSpan>[
          TextSpan(text: beforeMatch),
          TextSpan(
            text: match,
            style: TextStyle(
                fontWeight: FontWeight.bold, color: Theme.of(context).colorScheme.primary),
          ),
          TextSpan(text: afterMatch),
        ],
      ),
    );
  }

  Widget _buildHighlightedSummarySnippet(String text, String query) {
    final lowerText = text.toLowerCase();
    final lowerQuery = query.toLowerCase();
    final matchIndex = lowerText.indexOf(lowerQuery);

    if (matchIndex == -1) {
      return _buildSummarySnippet(context, text);
    }

    const snippetLength = 50;
    final start = max(0, matchIndex - snippetLength);
    final end = min(text.length, matchIndex + query.length + snippetLength);

    String beforeMatchSnippet = text.substring(start, matchIndex);
    if (start > 0) {
      beforeMatchSnippet = '...${beforeMatchSnippet.substring(3)}';
    }
    final match = text.substring(matchIndex, matchIndex + query.length);
    String afterMatchSnippet = text.substring(matchIndex + query.length, end);
    if (end < text.length) {
      afterMatchSnippet =
          '${afterMatchSnippet.substring(0, afterMatchSnippet.length - 3)}...';
    }

    return RichText(
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      text: TextSpan(
        style: TextStyle(fontSize: 14, color: Theme.of(context).colorScheme.tertiary),
        children: <TextSpan>[
          TextSpan(text: beforeMatchSnippet),
          TextSpan(
            text: match,
            style: TextStyle(
                fontWeight: FontWeight.bold, color: Theme.of(context).colorScheme.primary),
          ),
          TextSpan(text: afterMatchSnippet),
        ],
      ),
    );
  }

  Widget _buildSummarySnippet(BuildContext context, String text) {
    const snippetLength = 100;
    String snippet = text;
    if (text.length > snippetLength) {
      snippet = '${text.substring(0, snippetLength)}...';
    }
    return Text(
      snippet,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
          fontSize: 14, color: Theme.of(context).extension<AppColors>()!.subtitleText, fontWeight: FontWeight.w100),
    );
  }

  String _formatDuration(int seconds) {
    final duration = Duration(seconds: seconds);
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    if (duration.inHours > 0) {
      String twoDigitHours = twoDigits(duration.inHours);
      return "$twoDigitHours:$twoDigitMinutes:$twoDigitSeconds";
    }
    return "$twoDigitMinutes:$twoDigitSeconds";
  }
}
