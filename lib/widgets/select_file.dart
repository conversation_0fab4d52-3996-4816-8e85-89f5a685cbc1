import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import 'package:sintesy_app/services/sintesy_service.dart';
import 'package:sintesy_app/services/sintesy_local_crud.dart';
import 'package:sintesy_app/services/user_plan_manager.dart';
import 'package:sintesy_app/utils/call_plan.dart';
import 'package:uuid/uuid.dart';
import 'package:sintesy_app/providers/connection_checker.dart';
import 'package:sintesy_app/services/sintesy_sync_service.dart';

class AudioFileSelector extends StatefulWidget {
  const AudioFileSelector({super.key});

  @override
  State<AudioFileSelector> createState() => _AudioFileSelectorState();
}

class _FakeProgressDialog extends StatefulWidget {
  final String title;
  final VoidCallback onComplete;

  const _FakeProgressDialog({
    required this.title,
    required this.onComplete,
  });

  @override
  State<_FakeProgressDialog> createState() => _FakeProgressDialogState();
}

class _FakeProgressDialogState extends State<_FakeProgressDialog> {
  double _progress = 0.0;
  late Timer _timer;

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      setState(() {
        if (_progress < 0.95) {
          _progress += (1.0 - _progress) * 0.02;
        } else if (_progress < 0.97) {
          _progress += 0.0001;
        } else {
          _progress = 1.0;
          _timer.cancel();
          widget.onComplete();
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted && Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            }
          });
        }
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final progress = (_progress * 100).toInt();

    return AlertDialog(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(
            value: _progress,
          ),
          const SizedBox(height: 16),
          Text(
            '$progress% - ${widget.title}',
          ),
        ],
      ),
    );
  }
}

class _AudioFileSelectorState extends State<AudioFileSelector> {
  File? selectedFile;
  bool isProcessing = false;
  final SintesyService sintesyService = SintesyService();
  final SintesyCrud sintesyCrud = SintesyCrud();
  late UserPlanProvider userPlanProvider;
  String? selectedFileName;
  final SintesySyncService sintesySyncService = SintesySyncService();

  @override
  void initState() {
    super.initState();
    userPlanProvider = Provider.of<UserPlanProvider>(context, listen: false);
    _handleSharedAudio();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _handleSharedAudio() async {
    ReceiveSharingIntent.instance
        .getInitialMedia()
        .then((List<SharedMediaFile> files) {
      if (files.isNotEmpty) _processSharedFile(files.first);
    });

    ReceiveSharingIntent.instance
        .getMediaStream()
        .listen((List<SharedMediaFile> files) {
      if (files.isNotEmpty) _processSharedFile(files.first);
    });
  }

  Future<void> _processSharedFile(SharedMediaFile sharedFile) async {
    setState(() {
      isProcessing = true;
    });
    try {
      final file = File(sharedFile.path);
      final extension = sharedFile.path.split('.').last.toLowerCase();

      if (!['mp3', 'wav', 'm4a', 'mp4', 'ogg', 'opus'].contains(extension)) {
        throw Exception('Formato de arquivo não suportado');
      }

      final documentDir = await getApplicationDocumentsDirectory();
      String uuid = const Uuid().v4().replaceAll('-', "");
      String newPath = '${documentDir.path}/$uuid.wav';
      File renamedFile = await file.copy(newPath);
      if (!mounted) return;
      setState(() {
        selectedFile = renamedFile;
        selectedFileName = sharedFile.path.split('/').last;
      });
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Erro ao processar áudio: ${e.toString()}'),
        backgroundColor: Theme.of(context).colorScheme.error,
      ));
    } finally {
      if (mounted) {
        setState(() {
          isProcessing = false;
        });
      }
    }
  }

  Future<void> pickAudioFile() async {
    int todayDuration = await sintesyCrud.getTodayTotalDuration() ~/ 60;
    if (!mounted) return;

    if (todayDuration > (userPlanProvider.userPlan?.minuteLimit ?? 15) &&
        !(userPlanProvider.userPlan?.isPremium ?? false)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Limite diário atingido. Aguarde até amanhã'),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextButton(
                    onPressed: () {
                      callPlan(context);
                    },
                    child: const Text('Se torne PRO'),
                  ),
                ],
              ),
            ],
          ),
          duration: const Duration(seconds: 5),
        ),
      );
      return;
    }
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['mp3', 'wav', 'm4a', 'mp4'],
      );

      if (!mounted) return;

      if (result != null) {
        setState(() {
          selectedFile = File(result.files.single.path!);
          selectedFileName = result.files.single.name;
        });
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          backgroundColor: Theme.of(context).colorScheme.error,
          content: Text('Erro ao selecionar arquivo: ${e.toString()}'),
        ),
      );
    }
  }

  Future<void> _processAudio() async {
    if (selectedFile == null) return;

    setState(() {
      isProcessing = true;
    });

    try {
      final documentDir = await getApplicationDocumentsDirectory();
      String originalPath = selectedFile!.path;
      File originalFile = File(originalPath);
      String uuid = const Uuid().v4().replaceAll('-', "");
      String newPath = '${documentDir.path}/$uuid.wav';
      File renamedFile = await originalFile.copy(newPath);
      var sintesy = await sintesyService.saveSintesyLocal(
          '// Audio File', renamedFile.path);
      if (!mounted) return;
      bool hasInternet =
          Provider.of<ConnectionProvider>(context, listen: false).status;

      if (hasInternet) {
        if (!mounted) return;
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (dialogContext) => _FakeProgressDialog(
            title: 'Enviando arquivo...',
            onComplete: () {},
          ),
        );

        try {
          final result = await sintesySyncService.uploadLocalSintesy(sintesy);

          if (result == 403) {
            await sintesyCrud.delete(sintesy.uuid);
            if (mounted) Navigator.of(context).pop();
            if (mounted) callPlan(context);
            return;
          }

          if (mounted) Navigator.of(context).pop();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                backgroundColor: Theme.of(context).colorScheme.primary,
                content: const Text('Arquivo enviado com sucesso!'),
              ),
            );
          }
        } catch (e) {
          await sintesyCrud.delete(sintesy.uuid);

          if (mounted) Navigator.of(context).pop();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                backgroundColor: Theme.of(context).colorScheme.error,
                content: Text('Erro ao enviar arquivo: ${e.toString()}'),
              ),
            );
          }
          return;
        }
      } else {
        if (mounted) Navigator.of(context).pop();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Arquivo salvo localmente. Será enviado quando houver conexão.'),
              duration: Duration(seconds: 3),
            ),
          );
        }
      }

      setState(() {
        selectedFile = null;
        selectedFileName = null;
      });
    } catch (e) {
      if (mounted) Navigator.of(context).pop();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            backgroundColor: Theme.of(context).colorScheme.error,
            content: Text('Erro ao processar arquivo: ${e.toString()}'),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isProcessing = false;
        });
      }
    }
  }

  void _cancelSelection() {
    setState(() {
      selectedFile = null;
      selectedFileName = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: isProcessing ? null : pickAudioFile,
      borderRadius: BorderRadius.circular(12.0),
      child: Container(
          width: 240,
          height: selectedFileName != null ? 120 : 60,
          padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 6.0),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12.0),
            border: Border.all(
              color: selectedFileName != null
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.tertiary,
              width: 1.0,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Icon(
                    isProcessing ? Icons.hourglass_empty : Icons.audio_file,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            isProcessing
                                ? 'Processando...'
                                : selectedFileName != null
                                    ? 'Arquivo:'
                                    : 'Selecionar Arquivo',
                            style: TextStyle(
                              fontSize: 18.0,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.secondary,
                            ),
                          ),
                          if (selectedFileName != null)
                            Text(
                              selectedFileName!,
                              style: TextStyle(
                                  fontSize: 14.0,
                                  color:
                                      Theme.of(context).colorScheme.secondary),
                              overflow: TextOverflow.ellipsis,
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              if (selectedFileName != null)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton(
                      onPressed: _cancelSelection,
                      child: Text(
                        'Cancelar',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.error,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: isProcessing ? null : _processAudio,
                      child: Text(
                        'Enviar',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ),
                  ],
                ),
            ],
          )),
    );
  }
}
