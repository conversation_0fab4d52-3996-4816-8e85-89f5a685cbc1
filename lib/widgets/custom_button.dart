import 'package:flutter/material.dart';
import '../themes/light_theme.dart';

class CustomButton extends StatelessWidget {
  final double width, height;
  final bool isPrimary;
  final Function onPressed;
  final String text;

  const CustomButton({
    super.key,
    required this.width,
    required this.height,
    required this.isPrimary,
    required this.onPressed,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    final appColors = Theme.of(context).extension<AppColors>()!;

    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        color:  Theme.of(context).colorScheme.secondary,
        boxShadow: isPrimary
            ? [
                BoxShadow(
                  color: appColors.shadowColor,
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ]
            : [],
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color:
              isPrimary ? Colors.transparent : Theme.of(context).primaryColor,
        ),
      ),
      child: MaterialButton(
        onPressed: () => onPressed(),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 15,
            color: Theme.of(context).colorScheme.surface
          ),
        ),
      ),
    );
  }
}
