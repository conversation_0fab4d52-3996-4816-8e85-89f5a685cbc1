// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBTcMGDEj4EwuemYBGPEaJrjSzrI_ktgyA',
    appId: '1:370512297287:web:903fcd8cf0ac9bee3b6062',
    messagingSenderId: '370512297287',
    projectId: 'sintesy-9984e',
    authDomain: 'sintesy-9984e.firebaseapp.com',
    storageBucket: 'sintesy-9984e.firebasestorage.app',
    measurementId: 'G-GHS8Z059EC',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDHhk1m3Av9IYFeQduNzDG_WFT-Tzi2Pig',
    appId: '1:370512297287:android:4530fc1d7dbaaf4a3b6062',
    messagingSenderId: '370512297287',
    projectId: 'sintesy-9984e',
    storageBucket: 'sintesy-9984e.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC7Zgu2d69CdASrf9zsmZ2hhS0L5ZdqeLc',
    appId: '1:370512297287:ios:5a9479f115b7e9183b6062',
    messagingSenderId: '370512297287',
    projectId: 'sintesy-9984e',
    storageBucket: 'sintesy-9984e.firebasestorage.app',
    iosBundleId: 'sintesy.me.sintesyApp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBTcMGDEj4EwuemYBGPEaJrjSzrI_ktgyA',
    appId: '1:370512297287:web:701ee2246a8716ca3b6062',
    messagingSenderId: '370512297287',
    projectId: 'sintesy-9984e',
    authDomain: 'sintesy-9984e.firebaseapp.com',
    storageBucket: 'sintesy-9984e.firebasestorage.app',
    measurementId: 'G-XGWLGV2HVH',
  );

}