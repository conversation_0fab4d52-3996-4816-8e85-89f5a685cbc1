import 'package:flutter/material.dart';

class RecordingState with ChangeNotifier {
  String _recorderStatus = 'stop';
  Duration _recordingDuration = Duration.zero;
  String _filePath = '';
  double _dbLevel = 0.0;
  final List<double> _amplitudeLevels = List.filled(12, 0.0);

  String get recorderStatus => _recorderStatus;
  String get filePath => _filePath;
  Duration get recordingDuration => _recordingDuration;
  double get dbLevel => _dbLevel;
  List<double> get amplitudeLevels => _amplitudeLevels;

  void startRecording() {
    _recorderStatus = 'record';
    notifyListeners();
  }

  void stopRecording() {
    _recorderStatus = 'stop';
    _recordingDuration = Duration.zero;
    notifyListeners();
  }

  void pauseRecording() {
    _recorderStatus = 'pause';
    notifyListeners();
  }

  void updateRecordingDuration(Duration duration) {
    _recordingDuration = duration;
    notifyListeners();
  }

  void addOneSecond() {
    _recordingDuration += const Duration(seconds: 1);
    notifyListeners();
  }

  void setFilePath(String filePath) {
    _filePath = filePath;
    notifyListeners();
  }

  String get formatdDuration {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes =
        twoDigits(_recordingDuration.inMinutes.remainder(60));
    String twoDigitSeconds =
        twoDigits(_recordingDuration.inSeconds.remainder(60));
    if (_recordingDuration.inHours > 0) {
      String twoDigitHours =
          twoDigits(_recordingDuration.inHours.remainder(60));
      return "$twoDigitHours:$twoDigitMinutes:$twoDigitSeconds";
    }
    return "$twoDigitMinutes:$twoDigitSeconds";
  }

  void updateDbLevel(double db) {
    _dbLevel = db;
    const double minDb = 32.0;
    const double maxDb = 48.0;

    double normalizedValue = 0.0;
    if (db >= minDb) {
      normalizedValue = (db - minDb) / (maxDb - minDb);
      if (normalizedValue < 0.0) normalizedValue = 0.0;
      if (normalizedValue > 1.0) normalizedValue = 1.0;

      normalizedValue = normalizedValue * 0.8;
    }
    for (int i = 0; i < _amplitudeLevels.length - 1; i++) {
      _amplitudeLevels[i] = _amplitudeLevels[i + 1];
    }
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = 0.5 + (timestamp % 10) / 20.0;
    _amplitudeLevels[_amplitudeLevels.length - 1] = normalizedValue * random;
    if (timestamp % 5 == 0 && _amplitudeLevels.length > 3) {
      final randomIndex = timestamp % (_amplitudeLevels.length - 2) + 1;
      _amplitudeLevels[randomIndex] = _amplitudeLevels[randomIndex] * 0.7;
    }

    notifyListeners();
  }

  void resetAmplitudeLevels() {
    for (int i = 0; i < _amplitudeLevels.length; i++) {
      _amplitudeLevels[i] = 0.0;
    }
    _dbLevel = 0.0;
    notifyListeners();
  }
}
