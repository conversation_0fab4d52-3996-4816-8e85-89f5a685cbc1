import 'dart:async';

import 'package:flutter/material.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

class ConnectionProvider extends ChangeNotifier {
  final InternetConnectionChecker checker;
  bool _status = (InternetConnectionStatus.connected ==
      InternetConnectionStatus.connected);
  StreamSubscription<InternetConnectionStatus>? _subscription;

  ConnectionProvider(this.checker) {
    _checkInitialConnection();
    _listenToConnectionChanges();
  }

  bool get status => _status;

  Future<void> _checkInitialConnection() async {
    _status =
        await checker.connectionStatus == InternetConnectionStatus.connected;
    notifyListeners();
  }

  void _listenToConnectionChanges() {
    _subscription = checker.onStatusChange.listen((status) {
      _status = status == InternetConnectionStatus.connected;
      notifyListeners();
    });
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }
}
