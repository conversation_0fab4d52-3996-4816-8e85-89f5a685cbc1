import 'package:flutter/material.dart';
import 'package:sintesy_app/models/sintesy_model.dart';
import 'package:sintesy_app/services/sintesy_service.dart';

class SintesyState with ChangeNotifier {
  SintesyService sintesyService = SintesyService();
  List<SintesyModel> _sintesys = [];

  set sintesys(sintesysList) {
    if (_sintesys != sintesysList) {
      _sintesys = sintesysList;
      notifyListeners();
    }
  }

  Future<void> sintesysRefresh() async {
    sintesys = await sintesyService.getAll();
  }

  List<SintesyModel> get sintesys => _sintesys;
}
