import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Provider para gerenciar o tema da aplicação
/// Permite alternar entre tema claro, escuro e seguir o sistema
class ThemeProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  static const String _themeModeKey = 'theme_mode';

  ThemeMode get themeMode => _themeMode;

  /// Retorna true se o tema atual é escuro
  bool get isDarkMode {
    switch (_themeMode) {
      case ThemeMode.dark:
        return true;
      case ThemeMode.light:
        return false;
      case ThemeMode.system:
        // Para determinar se o sistema está em modo escuro,
        // você precisará acessar o MediaQuery no widget
        return false; // Valor padrão
    }
  }

  /// Construtor que carrega o tema salvo
  ThemeProvider() {
    _loadThemeMode();
  }

  /// Carrega o modo de tema salvo nas preferências
  Future<void> _loadThemeMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedThemeMode = prefs.getString(_themeModeKey);
      
      if (savedThemeMode != null) {
        _themeMode = _stringToThemeMode(savedThemeMode);
        notifyListeners();
      }
    } catch (e) {
      // Em caso de erro, mantém o tema padrão (system)
      debugPrint('Erro ao carregar tema: $e');
    }
  }

  /// Salva o modo de tema nas preferências
  Future<void> _saveThemeMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_themeModeKey, _themeModeToString(_themeMode));
    } catch (e) {
      debugPrint('Erro ao salvar tema: $e');
    }
  }

  /// Define o modo de tema
  Future<void> setThemeMode(ThemeMode themeMode) async {
    if (_themeMode != themeMode) {
      _themeMode = themeMode;
      await _saveThemeMode();
      notifyListeners();
    }
  }

  /// Alterna para o tema claro
  Future<void> setLightMode() async {
    await setThemeMode(ThemeMode.light);
  }

  /// Alterna para o tema escuro
  Future<void> setDarkMode() async {
    await setThemeMode(ThemeMode.dark);
  }

  /// Alterna para seguir o tema do sistema
  Future<void> setSystemMode() async {
    await setThemeMode(ThemeMode.system);
  }

  /// Alterna entre os modos de tema (claro -> escuro -> sistema -> claro...)
  Future<void> toggleTheme() async {
    switch (_themeMode) {
      case ThemeMode.light:
        await setDarkMode();
        break;
      case ThemeMode.dark:
        await setSystemMode();
        break;
      case ThemeMode.system:
        await setLightMode();
        break;
    }
  }

  /// Converte ThemeMode para String
  String _themeModeToString(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'light';
      case ThemeMode.dark:
        return 'dark';
      case ThemeMode.system:
        return 'system';
    }
  }

  /// Converte String para ThemeMode
  ThemeMode _stringToThemeMode(String themeMode) {
    switch (themeMode) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      case 'system':
        return ThemeMode.system;
      default:
        return ThemeMode.system;
    }
  }

  /// Retorna o nome do tema atual para exibição
  String get currentThemeName {
    switch (_themeMode) {
      case ThemeMode.light:
        return 'Claro';
      case ThemeMode.dark:
        return 'Escuro';
      case ThemeMode.system:
        return 'Sistema';
    }
  }

  /// Retorna o ícone correspondente ao tema atual
  IconData get currentThemeIcon {
    switch (_themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }
}
