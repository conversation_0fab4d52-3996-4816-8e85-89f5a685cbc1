import 'package:flutter/foundation.dart';
import 'package:flutter_sound/flutter_sound.dart';

class AudioState extends ChangeNotifier {
  String? _currentAudioUrl;
  String _currentTitle = '';
  bool _isPlaying = false;
  FlutterSoundPlayer? _currentPlayer;

  String? get currentAudioUrl => _currentAudioUrl;
  String get currentTitle => _currentTitle;
  bool get isPlayerVisible => _currentAudioUrl != null;
  bool get isPlaying => _isPlaying;

  void playAudio(String sintesyId, String url, String title) {
    if (_currentAudioUrl != url) {
      _currentPlayer?.stopPlayer();
      _currentAudioUrl = sintesyId;
      _currentTitle = title;
      _isPlaying = true;
      notifyListeners();
    }
  }

  void setPlayer(FlutterSoundPlayer player) {
    _currentPlayer = player;
  }

  void hidePlayer() {
    _currentAudioUrl = null;
    _currentTitle = '';
    _isPlaying = false;
    notifyListeners();
  }
}