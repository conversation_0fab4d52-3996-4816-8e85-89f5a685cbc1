import 'dart:async';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

class InAppPurchaseProvider with ChangeNotifier {
  final InAppPurchase _iap = InAppPurchase.instance;

  late StreamSubscription<List<PurchaseDetails>> _subscription;
  bool _isAvailable = false;
  List<ProductDetails> _products = [];
  List<PurchaseDetails> _purchases = [];

  // Getters
  bool get isAvailable => _isAvailable;
  List<ProductDetails> get products => _products;
  List<PurchaseDetails> get purchases => _purchases;

  InAppPurchaseProvider() {
    _initialize();
  }

  void _initialize() async {
    _isAvailable = await _iap.isAvailable();
    if (!_isAvailable) return;

    // Ouvir atualizações nas compras
    _subscription = _iap.purchaseStream.listen(
      _onPurchaseUpdate,
      onError: _onPurchaseError,
    );

    notifyListeners();
  }

  Future<void> fetchProducts(List<String> productIds) async {
    if (!_isAvailable) return;

    final ProductDetailsResponse response =
        await _iap.queryProductDetails(productIds.toSet());
    if (response.error == null) {
      _products = response.productDetails;
      notifyListeners();
    }
  }

  void buySubscription(ProductDetails product) {
    final PurchaseParam purchaseParam = PurchaseParam(productDetails: product);
    _iap.buyNonConsumable(purchaseParam: purchaseParam);
  }

  void _onPurchaseUpdate(List<PurchaseDetails> details) {
    _purchases = details;
    for (var purchase in details) {
      if (purchase.status == PurchaseStatus.purchased) {
        _iap.completePurchase(purchase);
        // Adicione sua lógica de validação de compra aqui
      }
    }
    notifyListeners();
  }

  void _onPurchaseError(dynamic error) {
    print('Erro na compra: $error');
    notifyListeners();
  }

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}
