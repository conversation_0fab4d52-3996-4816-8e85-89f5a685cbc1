import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:sintesy_app/services/payment_client.dart'; // Importe o PaymentClient

// +++++++++++++++++++++++++++++++++++
// ++ STRIPE PAYMENT INITIALIZATION ++
// +++++++++++++++++++++++++++++++++++

Future<bool> initPay(context, {String? couponCode}) async {
  try {
    PaymentClient paymentClient = PaymentClient();
    Map<String, dynamic> customer = await paymentClient.createCustomer();
    Navigator.of(context).pop();
    await createCreditCard(customer['id'], customer['client_secret']);
    final subscription = await paymentClient.createSubscription(customer['id'], couponCode: couponCode);
    return subscription['status'] == "Subscription active!";
  } catch (e) {
    print("Erro ao inicializar o pagamento: $e");
    return false;
  }
}

Future<void> createCreditCard(
  String customerId,
  String paymentIntentClientSecret,
) async {
  try {
    await Stripe.instance.initPaymentSheet(
      paymentSheetParameters: SetupPaymentSheetParameters(
        style: ThemeMode.light,
        merchantDisplayName: 'Sintesy Pro',
        customerId: customerId,
        setupIntentClientSecret: paymentIntentClientSecret,
        googlePay: const PaymentSheetGooglePay(
          testEnv: false,
          currencyCode: "BRL",
          merchantCountryCode: "BR",
        ),
      ),
    );

    await Stripe.instance.presentPaymentSheet();
    print("Pagamento realizado com sucesso!");
  } catch (e) {
    if (e is StripeException) {
      print("Erro no Stripe: ${e.error.localizedMessage}");
    } else {
      print("Erro inesperado: $e");
    }
  }
}
