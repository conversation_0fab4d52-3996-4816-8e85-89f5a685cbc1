import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/main.dart';
import 'package:sintesy_app/providers/connection_checker.dart';
import 'package:sintesy_app/services/payment_client.dart';
import 'package:sintesy_app/widgets/subs.dart';

Future<void> callPlan(context) async {
  final internet =
      Provider.of<ConnectionProvider>(context!, listen: false).status;

  if (internet) {
    PaymentClient paymentClient = PaymentClient();
    final Map<String, dynamic> plan = await paymentClient.getSubscription();
    showSubscriptionModal(context, plan);
  } else {
    scaffoldMessengerKey.currentState?.showSnackBar(
      const SnackBar(
        backgroundColor: Colors.black,
        content: Text('Sem conexão com a internet'),
      ),
    );
  }
}
