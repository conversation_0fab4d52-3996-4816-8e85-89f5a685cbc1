// Modelo de Customer
class UserPlan {
  bool? isPremium;
  DateTime? limitReachedDate;
  int? limitMinutesTime;
  int? minuteLimit;
  bool? limited;
  String? whatsapp;
  bool? whatsappEnabled;

  UserPlan(
      {required this.isPremium,
      required this.limitReachedDate,
      required this.limitMinutesTime,
      required this.minuteLimit,
      required this.limited,
      required this.whatsapp,
      required this.whatsappEnabled});

  factory UserPlan.fromJson(Map<String, dynamic> json) {
    return UserPlan(
      isPremium: json['is_premium'],
      limitReachedDate: json['limit_reached_date'] != null
          ? DateTime.parse(json['limit_reached_date'])
          : null,
      limitMinutesTime: json['limit_minutes_time'],
      minuteLimit: json['minute_limit'],
      limited: json['limit'],
      whatsapp: json['whatsapp'],
      whatsappEnabled: json['whatsapp_enabled'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'is_premium': isPremium,
      'limit_reached_date': limitReachedDate?.toIso8601String(),
      'limit_minutes_time': limitMinutesTime,
      'minute_limit': minuteLimit,
      'limited': limited,
      'whatsapp': whatsapp,
      'whatsapp_enabled': whatsappEnabled,
    };
  }
}
