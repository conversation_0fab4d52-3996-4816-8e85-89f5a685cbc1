import 'dart:convert';

class Customer {
  int? id;
  String? email;
  String? token;

  Customer({required this.id, required this.email, required this.token});

  factory Customer.fromReqBody(dynamic body) {
    Map<String, dynamic> json;

    if (body is String) {
      json = jsonDecode(body);
    } else if (body is Map<String, dynamic>) {
      json = body;
    } else {
      throw Exception('Unsupported body type: ${body.runtimeType}');
    }

    return Customer(
      id: json['id'],
      email: json['email'],
      token: json['token'],
    );
  }

  factory Customer.empty() {
    return Customer(
      id: null,
      email: null,
      token: null,
    );
  }

  void printAttributes() {
    print("id: $id\n");
    print("email: $email\n");
    print("token: $token\n");
  }
}
