class Sintesy {
  String id;
  DateTime createdDate;
  String updatedDate;
  String text;
  int? duration;
  String summary;
  String name;
  String summarySpeechUrl;
  String mindmap;
  String checklist;
  String roadmap;
  String source;
  String status;
  Map<String, dynamic>? attendeeBot;

  Sintesy(
      {required this.id,
      required this.createdDate,
      required this.updatedDate,
      required this.text,
      this.duration,
      required this.summary,
      required this.name,
      required this.summarySpeechUrl,
      required this.mindmap,
      required this.checklist,
      required this.roadmap,
      required this.source,
      required this.status,
      this.attendeeBot});

  factory Sintesy.fromJson(Map<String, dynamic> json) {
    return Sintesy(
        id: json['id'],
        createdDate: DateTime.parse(json['created_date']),
        updatedDate: json['updated_date'],
        text: json['text'] ?? '',
        duration: json['duration'],
        summary: json['summary'] ?? '',
        name: json['name'],
        summarySpeechUrl: json['summary_speech_url'],
        mindmap: json['mindmap'],
        checklist: json['checklist'],
        roadmap: json['roadmap'],
        source: json['source'],
        status: json['status'],
        attendeeBot: json['attendee_bot']);
  }
}
