import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
part 'sintesy_model.g.dart';

@HiveType(typeId: 1)
class SintesyModel extends HiveObject {
  @HiveField(0)
  late String uuid;

  @HiveField(1)
  late String name;

  @HiveField(2)
  String? path;

  @HiveField(3)
  int? duration;

  @HiveField(4)
  String? summary;

  @HiveField(5)
  String? text;

  @HiveField(6)
  late DateTime createdDate;

  @HiveField(7)
  String? summarySpeechUrl;

  @HiveField(8)
  String? mindmap;

  @HiveField(9)
  String? checklist;

  @HiveField(10)
  String? roadmap;

  @HiveField(11)
  bool? deleted;

  @HiveField(12)
  String? localStatus;

  @HiveField(13)
  String? source;

  @HiveField(14)
  String? status;

  @HiveField(15)
  AttendeeBotModel? attendeeBot;

  SintesyModel(
      {required this.name,
      this.path,
      String? uuid,
      this.duration,
      this.summary,
      this.text,
      required this.createdDate,
      this.summarySpeechUrl,
      this.mindmap,
      this.checklist,
      this.roadmap,
      this.deleted,
      this.localStatus,
      this.source,
      this.status,
      this.attendeeBot})
      : uuid = uuid ?? const Uuid().v4();
}

@HiveType(typeId: 2)
class AttendeeBotModel extends HiveObject {
  @HiveField(0)
  String? meetingUrl;

  @HiveField(1)
  String? botStatus;

  @HiveField(2)
  String? avatar;

  AttendeeBotModel({
    this.meetingUrl,
    this.botStatus,
    this.avatar,
  });

  factory AttendeeBotModel.fromJson(Map<String, dynamic> json) {
    return AttendeeBotModel(
      meetingUrl: json['meeting_url'],
      botStatus: json['bot_status'],
      avatar: json['avatar'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'meeting_url': meetingUrl,
      'bot_status': botStatus,
      'avatar': avatar,
    };
  }
}
