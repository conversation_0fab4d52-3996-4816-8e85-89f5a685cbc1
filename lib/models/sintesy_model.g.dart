// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sintesy_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SintesyModelAdapter extends TypeAdapter<SintesyModel> {
  @override
  final int typeId = 1;

  @override
  SintesyModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SintesyModel(
      name: fields[1] as String,
      path: fields[2] as String?,
      uuid: fields[0] as String?,
      duration: fields[3] as int?,
      summary: fields[4] as String?,
      text: fields[5] as String?,
      createdDate: fields[6] as DateTime,
      summarySpeechUrl: fields[7] as String?,
      mindmap: fields[8] as String?,
      checklist: fields[9] as String?,
      roadmap: fields[10] as String?,
      deleted: fields[11] as bool?,
      localStatus: fields[12] as String?,
      source: fields[13] as String?,
      status: fields[14] as String?,
      attendeeBot: fields[15] as AttendeeBotModel?,
    );
  }

  @override
  void write(BinaryWriter writer, SintesyModel obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.uuid)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.path)
      ..writeByte(3)
      ..write(obj.duration)
      ..writeByte(4)
      ..write(obj.summary)
      ..writeByte(5)
      ..write(obj.text)
      ..writeByte(6)
      ..write(obj.createdDate)
      ..writeByte(7)
      ..write(obj.summarySpeechUrl)
      ..writeByte(8)
      ..write(obj.mindmap)
      ..writeByte(9)
      ..write(obj.checklist)
      ..writeByte(10)
      ..write(obj.roadmap)
      ..writeByte(11)
      ..write(obj.deleted)
      ..writeByte(12)
      ..write(obj.localStatus)
      ..writeByte(13)
      ..write(obj.source)
      ..writeByte(14)
      ..write(obj.status)
      ..writeByte(15)
      ..write(obj.attendeeBot);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SintesyModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AttendeeBotModelAdapter extends TypeAdapter<AttendeeBotModel> {
  @override
  final int typeId = 2;

  @override
  AttendeeBotModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AttendeeBotModel(
      meetingUrl: fields[0] as String?,
      botStatus: fields[1] as String?,
      avatar: fields[2] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, AttendeeBotModel obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.meetingUrl)
      ..writeByte(1)
      ..write(obj.botStatus)
      ..writeByte(2)
      ..write(obj.avatar);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AttendeeBotModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
