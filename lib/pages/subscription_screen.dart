import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/providers/in_app_purchase_provider.dart';

class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  _SubscriptionScreenState createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen> {
  final List<String> _productIds = [
    'com.example.monthly', // Substitua pelos IDs reais
    'com.example.yearly',
  ];

  @override
  void initState() {
    super.initState();
    final provider = Provider.of<InAppPurchaseProvider>(context, listen: false);
    provider.fetchProducts(_productIds);
  }

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<InAppPurchaseProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Assinaturas'),
      ),
      body: provider.isAvailable
          ? ListView.builder(
              itemCount: provider.products.length,
              itemBuilder: (context, index) {
                final product = provider.products[index];
                return ListTile(
                  title: Text(product.title),
                  subtitle: Text(product.description),
                  trailing: Text(product.price),
                  onTap: () {
                    provider.buySubscription(product);
                  },
                );
              },
            )
          : const Center(child: Text('Compras não disponíveis')),
    );
  }
}
