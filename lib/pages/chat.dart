import 'package:flutter/material.dart';
import 'package:sintesy_app/services/chat_rest.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  _ChatScreenState createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _textController = TextEditingController();
  final List<ChatMessage> _messages = [];
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = false;

  Future<void> _askQuestion(String question) async {
    setState(() {
      _isLoading = true;
      _messages.add(ChatMessage(text: question, isUser: true));
      _textController.clear(); // Clear input after sending
    });

    // Scroll to the bottom after adding the user's message
    _scrollToBottom();

    try {
      final responseMap = await SintesyChatAPI().askSintesy(question);
      final responseText = responseMap['answer'] as String? ?? 'Erro ao obter resposta'; // Extract answer or use default error message
      setState(() {
        _messages.add(ChatMessage(text: responseText, isUser: false));
      });
    } catch (e) {
      setState(() {
        _messages.add(ChatMessage(
            text: 'Erro: $e', isUser: false)); // consistent error display
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
       _scrollToBottom(); // Scroll after receiving response
    }

  }

  void _scrollToBottom() {
    // Use WidgetsBinding to ensure the layout is complete before scrolling.
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chat Sintesy'),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(  
              controller: _scrollController,
              padding: const EdgeInsets.all(16.0),
              itemCount: _messages.length + (_isLoading ? 1 : 0), // +1 for loading
              itemBuilder: (context, index) {
                if (index < _messages.length) {
                    return _buildChatMessage(_messages[index]);
                } else {
                  //loading indicator.
                  return const Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.0),
                    child: Center(child: CircularProgressIndicator()),
                  );
                }
              },
            ),
          ),
          _buildInputArea(),
        ],
      ),
    );
  }

  Widget _buildChatMessage(ChatMessage message) {
    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
        padding: const EdgeInsets.all(12.0),
        decoration: BoxDecoration(
          color: message.isUser
              ? Theme.of(context).colorScheme.secondary
              : Colors.grey[300],
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: message.isUser
            ? Text(message.text, style: const TextStyle(color: Colors.white))
            : MarkdownBody(data: message.text),
      ),
    );
  }

  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -2), // changes position of shadow
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _textController,
              decoration: InputDecoration(
                hintText: 'Faça uma pergunta...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24.0),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
              ),
              onSubmitted: (value) {
                if (value.isNotEmpty) {
                  _askQuestion(value);
                }
              },
            ),
          ),
          IconButton(
            icon: Icon(Icons.send, color: Theme.of(context).colorScheme.primary),
            onPressed: () {
              if (_textController.text.isNotEmpty) {
                _askQuestion(_textController.text);
              }
            },
          ),
        ],
      ),
    );
  }
}

class ChatMessage {
  final String text;
  final bool isUser;

  ChatMessage({required this.text, required this.isUser});
}