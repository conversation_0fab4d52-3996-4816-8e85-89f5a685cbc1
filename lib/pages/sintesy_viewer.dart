import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/models/sintesy_model.dart';
import 'package:sintesy_app/providers/audio_player_provider.dart';
import 'package:sintesy_app/providers/sintesy_state.dart';
import 'package:sintesy_app/services/rating_service.dart';
import 'package:sintesy_app/services/user_plan_manager.dart';
import 'package:sintesy_app/utils/call_plan.dart';
import 'package:sintesy_app/widgets/rating_widget.dart';
import 'package:url_launcher/url_launcher.dart';

class SintesyViewer extends StatefulWidget {
  final SintesyModel sintesy;
  const SintesyViewer({super.key, required this.sintesy});

  @override
  State<SintesyViewer> createState() => _SintesyViewerState();
}

enum SintesyTab {
  summary(Icons.auto_awesome, 'Resumo'),
  checklist(Icons.checklist, 'Checklist'),
  roadmap(Icons.format_list_bulleted, 'Roadmap'),
  transcription(Icons.format_align_center, 'Transcrição'),
  mindmap(Icons.account_tree, 'Mindmap');

  const SintesyTab(this.icon, this.label);
  final IconData icon;
  final String label;
}

class _SintesyViewerState extends State<SintesyViewer>
    with TickerProviderStateMixin {
  late TabController _tabController;
  InAppWebViewController? _webViewController;
  SintesyModel? _currentSintesy;
  List<SintesyTab> _availableTabs = [];

  @override
  void initState() {
    super.initState();
    _currentSintesy = widget.sintesy;
    _initTabs();
  }

  @override
  void didUpdateWidget(covariant SintesyViewer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.sintesy.uuid != widget.sintesy.uuid) {
      _currentSintesy = widget.sintesy;
      _initTabs();
    }
  }

  void _initTabs() {
    _availableTabs = [SintesyTab.summary];
    
    if (_hasContent(_currentSintesy?.checklist)) {
      _availableTabs.add(SintesyTab.checklist);
    }
    if (_hasContent(_currentSintesy?.roadmap)) {
      _availableTabs.add(SintesyTab.roadmap);
    }
    
    _availableTabs.add(SintesyTab.transcription);
    
    if (_hasContent(_currentSintesy?.mindmap)) {
      _availableTabs.add(SintesyTab.mindmap);
    }

    _tabController = TabController(length: _availableTabs.length, vsync: this);
    _tabController.addListener(() {
      setState(() {});
    });
  }

  bool _hasContent(String? content) {
    return content != null && content.isNotEmpty;
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  SintesyModel? _getUpdatedSintesy(List<SintesyModel> sintesys) {
    return sintesys.firstWhere(
      (element) => element.uuid == widget.sintesy.uuid,
      orElse: () => widget.sintesy,
    );
  }

  void _updateSintesyIfNeeded(List<SintesyModel> sintesys) {
    final updatedSintesy = _getUpdatedSintesy(sintesys);
    if (updatedSintesy != null && _currentSintesy?.uuid != updatedSintesy.uuid) {
      _currentSintesy = updatedSintesy;
      _initTabs();
    }
  }

  void _copyActiveTabText() {
    if (_tabController.index >= _availableTabs.length) return;
    
    final currentTab = _availableTabs[_tabController.index];
    final textToCopy = _getTextForTab(currentTab);

    if (textToCopy.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: textToCopy));
      _showSnackBar('Texto copiado para a área de transferência');
    } else {
      _showSnackBar('Nenhum texto disponível para copiar');
    }
  }

  String _getTextForTab(SintesyTab tab) {
    switch (tab) {
      case SintesyTab.summary:
        return _currentSintesy?.summary ?? '';
      case SintesyTab.checklist:
        return _currentSintesy?.checklist ?? '';
      case SintesyTab.roadmap:
        return _currentSintesy?.roadmap ?? '';
      case SintesyTab.transcription:
        return _currentSintesy?.text ?? '';
      case SintesyTab.mindmap:
        return _currentSintesy?.summary ?? '';
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).size.height - 80,
          left: 20,
          right: 20,
        ),
      ),
    );
  }

  void _play() {
    if (_hasValidAudioUrl()) {
      final audioState = context.read<AudioState>();
      audioState.playAudio(
        _currentSintesy!.uuid,
        _currentSintesy!.summarySpeechUrl!,
        _currentSintesy!.name,
      );
    } else {
      _showSnackBar('Gerando voz para sintesy...');
    }
  }

  bool _hasValidAudioUrl() {
    return _currentSintesy?.summarySpeechUrl != null &&
           _currentSintesy!.summarySpeechUrl!.isNotEmpty;
  }

  List<Widget> _buildModernTabs() {
    return _availableTabs.asMap().entries.map((entry) {
      final index = entry.key;
      final tab = entry.value;
      return _buildModernTab(
        icon: tab.icon,
        label: tab.label,
        index: index,
        isSelected: _tabController.index == index,
      );
    }).toList();
  }

  Widget _buildModernTab({
    required IconData icon,
    required String label,
    required int index,
    required bool isSelected,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 400),
      curve: Curves.easeOutCubic,
      width: isSelected ? 120 : 60, // Largura animada
      child: GestureDetector(
        onTap: () {
          setState(() {
            _tabController.animateTo(index);
          });
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeInOut,
          margin: const EdgeInsets.symmetric(horizontal: 3),
          padding: EdgeInsets.symmetric(
            vertical: 10,
            horizontal: isSelected ? 12 : 6,
          ),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected
                  ? (Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black)
                  : Colors.transparent,
            ),
            borderRadius: BorderRadius.circular(10),
            boxShadow: isSelected ? [

            ] : [],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: isSelected ? 24 : 20,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              if (isSelected) ...[
                const SizedBox(width: 6),
                Flexible(
                  child: Text(
                    label,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _showSharePopup() {
    showDialog(
      context: context,
      builder: (BuildContext context) => _ShareDialog(
        sintesyUuid: widget.sintesy.uuid,
        onExportPDF: _exportToPDF,
        onExportDOCX: _exportToDOCX,
      ),
    );
  }

  Future<void> _exportToPDF({String fieldType = 'summary'}) async {
    if (_currentSintesy?.uuid == null) return;

    final urlString =
        'https://api.sintesy.me/sintesy/${_currentSintesy!.uuid}/pdf/?field=$fieldType';
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }

  Future<void> _exportToDOCX({String fieldType = 'summary'}) async {
    if (_currentSintesy?.uuid == null) return;

    final urlString =
        'https://api.sintesy.me/sintesy/${_currentSintesy!.uuid}/docx/?field=$fieldType';
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }


  @override
  Widget build(BuildContext context) {
    return Consumer<SintesyState>(
      builder: (context, sintesyState, child) {
        _updateSintesyIfNeeded(sintesyState.sintesys);
        return Scaffold(
          appBar: AppBar(
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
            title: Text(
              _currentSintesy?.name ?? '',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: 18,
              ),
            ),
            actions: [
              if (_hasValidAudioUrl())
                IconButton(
                  icon: Icon(
                    Icons.voice_chat,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  onPressed: _play,
                ),
              IconButton(
                icon: Icon(
                  Icons.copy,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                onPressed: _copyActiveTabText,
              ),
              IconButton(
                icon: Icon(
                  Icons.share,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                onPressed: _showSharePopup,
              ),
            ],
            centerTitle: false,
            iconTheme: IconThemeData(color: Theme.of(context).colorScheme.onSurface),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(60.0),
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(10),

                ),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: _buildModernTabs(),
                  ),
                ),
              ),
            ),
          ),
          body: IndexedStack(
            index: _tabController.index,
            children: _buildTabContent(),
          )
        );
      },
    );
  }

  List<Widget> _buildTabContent() {
    return _availableTabs.map((tab) => _buildContentForTab(tab)).toList();
  }

  Widget _buildContentForTab(SintesyTab tab) {
    switch (tab) {
      case SintesyTab.summary:
        return _MarkdownContent(data: _currentSintesy?.summary ?? '');
      case SintesyTab.checklist:
        return _MarkdownContent(data: _currentSintesy?.checklist ?? '');
      case SintesyTab.roadmap:
        return _MarkdownContent(data: _currentSintesy?.roadmap ?? '');
      case SintesyTab.transcription:
        return _MarkdownContent(data: _currentSintesy?.text ?? '');
      case SintesyTab.mindmap:
        return _MindmapContent(
          mindmapData: _currentSintesy?.mindmap ?? '',
          onWebViewCreated: (controller) => _webViewController = controller,
        );
    }
  }
}

class _MarkdownContent extends StatefulWidget {
  final String data;
  
  const _MarkdownContent({required this.data});

  @override
  State<_MarkdownContent> createState() => _MarkdownContentState();
}

class _MarkdownContentState extends State<_MarkdownContent> {
  final ScrollController _scrollController = ScrollController();
  bool _showRating = false;
  
  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }
  
  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }
  
  void _onScroll() {
    // Verifica se o usuário chegou próximo ao final do conteúdo
    if (_scrollController.position.pixels > 
        _scrollController.position.maxScrollExtent - 200 && !_showRating) {
      setState(() {
        _showRating = true;
      });
    } 
    // Esconde o widget se o usuário rolar para cima (10% da tela)
    else if (_showRating && _scrollController.position.pixels < 
        _scrollController.position.maxScrollExtent - (_scrollController.position.viewportDimension * 0.5)) {
      setState(() {
        _showRating = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Obtém o UUID da sintesy atual do widget SintesyViewer
    final sintesyViewer = context.findAncestorWidgetOfExactType<SintesyViewer>();
    final String sintesyUuid = sintesyViewer?.sintesy.uuid ?? '';
    
    return Column(
      children: [
        Expanded(
          child: Markdown(
            controller: _scrollController,
            selectable: true,
            data: widget.data,
            styleSheet: MarkdownStyleSheet(
              p: const TextStyle(fontSize: 16),
            ),
          ),
        ),
        AnimatedCrossFade(
          duration: const Duration(milliseconds: 300),
          crossFadeState: _showRating 
              ? CrossFadeState.showSecond 
              : CrossFadeState.showFirst,
          firstChild: const SizedBox(height: 0),
          secondChild: Padding(
            padding: const EdgeInsets.all(16.0),
            child: SintesyRatingWidget(
              sintesyUuid: sintesyUuid,
              onRatingSubmitted: (success, message) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(message),
                    duration: const Duration(seconds: 2),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}

class _MindmapContent extends StatefulWidget {
  final String mindmapData;
  final Function(InAppWebViewController) onWebViewCreated;
  
  const _MindmapContent({
    required this.mindmapData,
    required this.onWebViewCreated,
  });

  @override
  State<_MindmapContent> createState() => _MindmapContentState();
}

class _MindmapContentState extends State<_MindmapContent> {
  bool _showRating = false;
  InAppWebViewController? _controller;
  
  @override
  void initState() {
    super.initState();
    // Mostrar a avaliação após um tempo para dar ao usuário a chance de ver o mindmap
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        setState(() {
          _showRating = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Obtém o UUID da sintesy atual do widget SintesyViewer
    final sintesyViewer = context.findAncestorWidgetOfExactType<SintesyViewer>();
    final String sintesyUuid = sintesyViewer?.sintesy.uuid ?? '';
    
    return Column(
      children: [
        Expanded(
          child: InAppWebView(
            initialUrlRequest: URLRequest(
              url: WebUri.uri(
                Uri.parse("https://dashboard.sintesy.me/mindmap?markdown=${Uri.encodeComponent(widget.mindmapData)}"),
              ),
            ),
            onWebViewCreated: (controller) {
              _controller = controller;
              widget.onWebViewCreated(controller);
            },
            onLoadStart: (controller, url) {
              debugPrint("Início do carregamento: $url");
            },
            onLoadStop: (controller, url) {
              debugPrint("Carregamento finalizado: $url");
            },
            onProgressChanged: (controller, progress) {
              debugPrint("Progresso: $progress%");
              // Quando o carregamento estiver completo, começamos a monitorar a interação
              if (progress == 100) {
                Future.delayed(const Duration(seconds: 5), () {
                  if (mounted && !_showRating) {
                    setState(() {
                      _showRating = true;
                    });
                  }
                });
              }
            },
          ),
        ),
        AnimatedCrossFade(
          duration: const Duration(milliseconds: 300),
          crossFadeState: _showRating 
              ? CrossFadeState.showSecond 
              : CrossFadeState.showFirst,
          firstChild: const SizedBox(height: 0),
          secondChild: Padding(
            padding: const EdgeInsets.all(16.0),
            child: SintesyRatingWidget(
              sintesyUuid: sintesyUuid,
              onRatingSubmitted: (success, message) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(message),
                    duration: const Duration(seconds: 2),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}

class _ShareDialog extends StatelessWidget {
  final String sintesyUuid;
  final Function({String fieldType}) onExportPDF;
  final Function({String fieldType}) onExportDOCX;
  
  const _ShareDialog({
    required this.sintesyUuid,
    required this.onExportPDF,
    required this.onExportDOCX,
  });

  @override
  Widget build(BuildContext context) {
    final String linkToShare = "app.sintesy.me/s/$sintesyUuid";
    String exportContentType = 'summary';
    final userPlanProvider = Provider.of<UserPlanProvider>(context, listen: false);
    final bool isPremium = userPlanProvider.userPlan?.isPremium ?? false;

    return AlertDialog(
      title: const Text('Compartilhar ou Exportar'),
      content: StatefulBuilder(
        builder: (BuildContext context, StateSetter setState) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Compartilhamento:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.black45,
                  fontSize: 14,
                ),
              ),
              _ShareOptionTile(
                icon: Icons.link,
                iconColor: Colors.blue,
                title: 'Link Compartilhável',
                subtitle: 'Copie e compartilhe o link de acesso',
                onTap: () => _handleShareLink(context, linkToShare, isPremium),
              ),
              const Divider(thickness: 1, height: 32, color: Colors.black12),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'Conteúdo para exportar',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                value: exportContentType,
                items: const [
                  DropdownMenuItem(
                    value: 'summary',
                    child: Text('Resumo'),
                  ),
                  DropdownMenuItem(
                    value: 'text',
                    child: Text('Transcrição Completa'),
                  ),
                ],
                onChanged: (String? newValue) {
                  setState(() {
                    exportContentType = newValue!;
                  });
                },
              ),
              const SizedBox(height: 16),
              _ShareOptionTile(
                icon: Icons.picture_as_pdf,
                iconColor: Colors.red,
                title: 'Exportar para PDF',
                subtitle: 'Baixe o documento em formato PDF',
                onTap: () => _handleExport(
                  context,
                  isPremium,
                  () => onExportPDF(fieldType: exportContentType),
                ),
              ),
              _ShareOptionTile(
                icon: Icons.edit_document,
                iconColor: Colors.blueGrey,
                title: 'Exportar para DOCX',
                subtitle: 'Baixe o documento em formato Word',
                onTap: () => _handleExport(
                  context,
                  isPremium,
                  () => onExportDOCX(fieldType: exportContentType),
                ),
              ),
            ],
          );
        },
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Fechar'),
        ),
      ],
    );
  }

  void _handleShareLink(BuildContext context, String link, bool isPremium) {
    if (isPremium) {
      Clipboard.setData(ClipboardData(text: link));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Link copiado para a área de transferência'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    } else {
      callPlan(context);
    }
  }

  void _handleExport(BuildContext context, bool isPremium, VoidCallback exportAction) {
    if (isPremium) {
      Navigator.of(context).pop();
      exportAction();
    } else {
      callPlan(context);
    }
  }
}

class _ShareOptionTile extends StatelessWidget {
  final IconData icon;
  final Color iconColor;
  final String title;
  final String subtitle;
  final VoidCallback onTap;
  
  const _ShareOptionTile({
    required this.icon,
    required this.iconColor,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Icon(icon, color: iconColor),
      title: Text(title),
      subtitle: Text(subtitle),
      onTap: onTap,
    );
  }
}
