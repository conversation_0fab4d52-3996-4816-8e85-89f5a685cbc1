import 'package:flutter/material.dart';
import 'package:sintesy_app/models/sintesy_model.dart';
import 'package:sintesy_app/services/sintesy_client.dart';
import 'package:sintesy_app/services/sintesy_local_crud.dart';
import 'package:sintesy_app/services/user_plan_manager.dart';
import 'package:sintesy_app/utils/call_plan.dart';
import 'package:provider/provider.dart';
import '../themes/light_theme.dart';

class NoteScreen extends StatefulWidget {
  final TextEditingController controller;

  const NoteScreen({super.key, required this.controller});

  @override
  _NoteScreenState createState() => _NoteScreenState();
}

class _NoteScreenState extends State<NoteScreen> {
  late FocusNode _focusNode;
  final SintesyAPI sintesyAPI = SintesyAPI();
  final int _maxTextLength = 500;
  bool _isTextLimitReached = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(_focusNode);
    });

    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _focusNode.dispose();
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onTextChanged() {
    if (widget.controller.text.length >= _maxTextLength &&
        !_isTextLimitReached) {
      setState(() {
        _isTextLimitReached = true;
      });
    } else if (widget.controller.text.length < _maxTextLength &&
        _isTextLimitReached) {
      setState(() {
        _isTextLimitReached = false;
      });
    }
  }

  Future<void> _saveNote() async {
    try {
      String noteText = widget.controller.text;

      if (noteText.isEmpty) {
        _showSnackBar('Faça uma anotação');
        return;
      }
      String name = noteText.length > 50 ? noteText.substring(0, 50) : noteText;
      final sintesy = await _saveSintesyLocal(name, noteText);
      var response = await sintesyAPI.createSintesy(
        sintesy.uuid,
        name,
        noteText,
      );

      if (response.statusCode == 403) {
        final SintesyCrud sintesyCrud = SintesyCrud();
        await sintesyCrud.delete(sintesy.uuid);
        if (mounted) {
          callPlan(context);
        }
        return;
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        if (mounted) {
          _showSnackBar('Anotação salva com sucesso!');
          FocusScope.of(context).unfocus();
        }
        widget.controller.clear();
        if (mounted) {
          Navigator.pop(context);
        }
      } else {
        final SintesyCrud sintesyCrud = SintesyCrud();
        await sintesyCrud.delete(sintesy.uuid);

        throw Exception('Erro ao salvar a anotação: ${response.body}');
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar('Erro ao salvar a anotação: $e');
      }
    }
  }

  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 1),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final userPlanProvider =
        Provider.of<UserPlanProvider>(context, listen: false);
    bool isPremium = userPlanProvider.userPlan?.isPremium ?? false;
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Theme.of(context).colorScheme.onSurface),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text('Anotação', style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          TextButton(
            onPressed:
                _saveNote, // Chama a função _saveNote ao clicar em Salvar
            child: Text(
              'Salvar',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).extension<AppColors>()!.backgroundGradientStart,
              Theme.of(context).extension<AppColors>()!.backgroundGradientEnd,
              Theme.of(context).extension<AppColors>()!.backgroundGradientMiddle,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              const SizedBox(height: 80),
              Expanded(
                child: Material(
                  elevation: 1,
                  borderRadius: BorderRadius.circular(12),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Stack(
                      children: [
                        TextField(
                          focusNode: _focusNode,
                          controller: widget.controller,
                          maxLines: null,
                          maxLength: _isTextLimitReached && !isPremium
                              ? _maxTextLength
                              : null,
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                            hintText: 'Digite suas anotações aqui...',
                            counterText: '',
                          ),
                          style: const TextStyle(fontSize: 20),
                          enabled: !_isTextLimitReached || isPremium,
                        ),
                        if (_isTextLimitReached && !isPremium)
                          Positioned(
                            bottom: 0,
                            right: 10,
                            child: Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Text(
                                'Limite Plano Free (${widget.controller.text.length}/$_maxTextLength)',
                                style: TextStyle(
                                    color: Theme.of(context).colorScheme.error,
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
              if (_isTextLimitReached && !isPremium)
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: ElevatedButton(
                    onPressed: () {
                      callPlan(context);
                    },
                    child: const Text('Desbloquear Limite'),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Future<SintesyModel> _saveSintesyLocal(String name, String text) async {
    final SintesyCrud sintesyCrud = SintesyCrud();
    SintesyModel sintesy =
        SintesyModel(name: name, createdDate: DateTime.now(), text: text);
    await sintesyCrud.add(sintesy);
    return sintesy;
  }
}
