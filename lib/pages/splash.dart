import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sintesy_app/pages/home.dart';
import 'package:sintesy_app/pages/welcome_page.dart';
import 'package:sintesy_app/providers/sintesy_state.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  _SplashPageState createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  late SharedPreferences prefs;
  String? sharedToken;
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    initializeData();
  }

  Future<void> initializeData() async {
    await initializeSharedPreferences();
    await Provider.of<SintesyState>(context, listen: false).sintesysRefresh();
    setState(() {
      _loading = false;
    });
  }


  Future<void> initializeSharedPreferences() async {
    prefs = await SharedPreferences.getInstance();
    sharedToken = prefs.getString('token');

  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return isLoggedIn ? const HomePage() : const WelcomePage();
  }

  bool get isLoggedIn => sharedToken != null;
}