import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sintesy_app/pages/home.dart';
import 'package:sintesy_app/services/user_client.dart';
import 'package:sintesy_app/services/cubit.dart';
import 'package:sintesy_app/services/shared_preferences_service.dart';
import 'package:sintesy_app/src/customer_model.dart';
import 'package:sintesy_app/widgets/custom_button.dart';
import 'package:sintesy_app/widgets/custom_form_field.dart';
import '../themes/light_theme.dart';

const List<String> scopes = <String>['email'];
GoogleSignIn _googleSignIn = GoogleSignIn(
  clientId:
      '178685358050-vqnt9dgcps3e2m637rfto5jd53jq7k3d.apps.googleusercontent.com',
  scopes: scopes,
);

class WelcomePage extends StatefulWidget {
  const WelcomePage({super.key});

  @override
  State<WelcomePage> createState() => _WelcomePageState();
}

class _WelcomePageState extends State<WelcomePage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  late StreamSubscription<bool> keyboardSubscription;
  bool _isLoading = false;

  double _imageHeight = 0.25;

  @override
  void initState() {
    super.initState();
    keyboardSubscription =
        KeyboardVisibilityController().onChange.listen((bool isVisible) {
      _updateImageHeightBasedOnKeyboard(isVisible);
    });
  }

  @override
  void dispose() {
    keyboardSubscription.cancel();
    super.dispose();
  }

  void _updateImageHeightBasedOnKeyboard(bool isVisible) {
    setState(() {
      _imageHeight = isVisible ? 0 : 0.25;
    });
  }

  Future<void> _handleSignIn(BuildContext context) async {
    if (_isLoading) return;

    setState(() => _isLoading = true);

    try {
      await _googleSignIn.signOut();
      final GoogleSignInAccount? googleAccount = await _googleSignIn.signIn();

      if (!mounted) return;

      if (googleAccount == null) {
        setState(() => _isLoading = false);
        return;
      }

      AuthAPI authAPI = AuthAPI();
      String? oauthCode = googleAccount.serverAuthCode;
      if (oauthCode != null) {
        var req = await authAPI.oauth(oauthCode);

        if (!mounted) return;

        if (req.statusCode == 200) {
          var customer = Customer.fromReqBody(req.data);
          BlocProvider.of<CustomerCubit>(context).login(customer);
          upDateSharedPreferences(customer.token!, customer.id!, customer.email!);
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomePage()),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Falha ao fazer login com Google'),
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
              margin: EdgeInsets.only(
                bottom: MediaQuery.of(context).size.height - 80,
                left: 20,
                right: 20,
              ),
            ),
          );
          setState(() => _isLoading = false);
        }
      } else {
        setState(() => _isLoading = false);
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro: ${e.toString()}'),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          margin: EdgeInsets.only(
            bottom: MediaQuery.of(context).size.height - 80,
            left: 20,
            right: 20,
          ),
        ),
      );
      setState(() => _isLoading = false);
    }
  }

  Future<void> _handleLogin(BuildContext context) async {
    if (_isLoading || !_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      AuthAPI authAPI = AuthAPI();
      var req = await authAPI.auth(_emailController.text, _passwordController.text);

      if (!mounted) return;

      if (req.statusCode == 200) {
        var customer = Customer.fromReqBody(req.data);
        BlocProvider.of<CustomerCubit>(context).login(customer);
        upDateSharedPreferences(customer.token!, customer.id!, customer.email!);
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const HomePage()),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Credenciais inválidas'),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.only(
              bottom: MediaQuery.of(context).size.height - 80,
              left: 20,
              right: 20,
            ),
          ),
        );
        setState(() => _isLoading = false);
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro: ${e.toString()}'),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          margin: EdgeInsets.only(
            bottom: MediaQuery.of(context).size.height - 80,
            left: 20,
            right: 20,
          ),
        ),
      );
      setState(() => _isLoading = false);
    }
  }

  Future<void> _handleCreateAccount(BuildContext context) async {
    if (_isLoading || !_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      AuthAPI authAPI = AuthAPI();
      // Since there's no specific account creation endpoint visible in the code,
      // we'll use the auth endpoint which might handle registration if credentials don't exist
      var req = await authAPI.auth(_emailController.text, _passwordController.text);

      if (!mounted) return;

      if (req.statusCode == 200) {
        var customer = Customer.fromReqBody(req.data);
        BlocProvider.of<CustomerCubit>(context).login(customer);
        upDateSharedPreferences(customer.token!, customer.id!, customer.email!);
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const HomePage()),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Não foi possível criar a conta. Tente novamente.'),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.only(
              bottom: MediaQuery.of(context).size.height - 80,
              left: 20,
              right: 20,
            ),
          ),
        );
        setState(() => _isLoading = false);
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro: ${e.toString()}'),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          margin: EdgeInsets.only(
            bottom: MediaQuery.of(context).size.height - 80,
            left: 20,
            right: 20,
          ),
        ),
      );
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: _buildUI(context),
      backgroundColor: Theme.of(context).colorScheme.surface,
    );
  }

  Widget _buildUI(BuildContext context) {
    return Stack(
      children: [
        _foregroundWidgets(context),
      ],
    );
  }

  Widget _foregroundWidgets(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        child: Center(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.sizeOf(context).height,
            ),
            child: IntrinsicHeight(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  _infoContainer(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _avatarImage(BuildContext context) {
    return Center(
      child: Image.asset(
        'assets/images/logo-login-page.png',
        width: MediaQuery.sizeOf(context).width,
        height: MediaQuery.sizeOf(context).height * _imageHeight,
        fit: BoxFit.contain,
      ),
    );
  }

  Widget _infoContainer(BuildContext context) {
    return AbsorbPointer(
      absorbing: _isLoading,
      child: Container(
        height: MediaQuery.sizeOf(context).height,
        width: MediaQuery.sizeOf(context).width,
        padding: EdgeInsets.symmetric(
          horizontal: MediaQuery.sizeOf(context).width * 0.04,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: Theme.of(context).extension<AppColors>()!.shadowColor,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: MediaQuery.sizeOf(context).width * 0.2),
            _avatarImage(context),
            Text(
              "Faça login ou crie uma conta",
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Theme.of(context).colorScheme.tertiary.withOpacity(0.6),
              ),
            ),
            _loginForm(context),
            Text("ou", style: TextStyle(
              color: Theme.of(context).colorScheme.tertiary,
            )),
            const SizedBox(height: 8),
            Opacity(
              opacity: _isLoading ? 0.7 : 1.0,
              child: Container(
                width: MediaQuery.sizeOf(context).width*0.83,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.onSurface,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: MaterialButton(
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  onPressed: () => _handleSignIn(context),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        'assets/images/logo-google.png',
                        height: 24,
                        width: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _isLoading ? "Carregando..." : "Login com Google",
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _loginForm(BuildContext context) {
    return Form(
      key: _formKey,
      child: Container(
        width: MediaQuery.sizeOf(context).width * 0.85,
        margin: EdgeInsets.symmetric(
          vertical: MediaQuery.sizeOf(context).height * 0.02,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _isLoading
                ? SizedBox(
                    width: MediaQuery.sizeOf(context).width,
                    height: 3,
                    child: LinearProgressIndicator(
                        color: Theme.of(context).colorScheme.primary,
                        backgroundColor: Colors.transparent,
                        minHeight: 8,
                      ),
                  )
                : const SizedBox(height: 8),
            const SizedBox(height: 5),
            Opacity(
              opacity: _isLoading ? 0.7 : 1.0,
              child: CustomFormField(
                controller: _emailController,
                label: "Email",
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Por favor, digite seu email';
                  }
                  if (!value.contains('@')) {
                    return 'Por favor, digite um email válido';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(height: 8),
            Opacity(
              opacity: _isLoading ? 0.7 : 1.0,
              child: CustomFormField(
                controller: _passwordController,
                label: "Senha",
                obscureText: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Por favor, digite sua senha';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(height: 8),
            Opacity(
              opacity: _isLoading ? 0.7 : 1.0,
              child: CustomButton(
                width: MediaQuery.sizeOf(context).width,
                height: 50,
                isPrimary: false,
                onPressed: () => _handleLogin(context),
                text: _isLoading ? "Carregando..." : "Login",
              ),
            ),
            const SizedBox(height: 8),
            Opacity(
              opacity: _isLoading ? 0.7 : 1.0,
              child: CustomButton(
                width: MediaQuery.sizeOf(context).width,
                height: 50,
                isPrimary: false,
                onPressed: () => _handleCreateAccount(context),
                text: _isLoading ? "Carregando..." : "Criar Conta",
              ),
            ),
            // const Text(
            //   "Redefinir senha",
            //   style: TextStyle(
            //     fontSize: 13,
            //     color: Color.fromARGB(255, 37, 109, 254),
            //     fontWeight: FontWeight.bold,
            //   ),
            // ),

          ],
        ),
      ),
    );
  }
}
