import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:flutter_stripe/flutter_stripe.dart';

import 'package:hive_flutter/hive_flutter.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sintesy_app/models/sintesy_model.dart';
import 'package:sintesy_app/pages/home.dart';
import 'package:sintesy_app/pages/splash.dart';
import 'package:sintesy_app/pages/subscription_screen.dart';
import 'package:sintesy_app/pages/welcome_page.dart';
import 'package:sintesy_app/providers/audio_player_provider.dart';
import 'package:sintesy_app/providers/connection_checker.dart';
import 'package:sintesy_app/providers/in_app_purchase_provider.dart';
import 'package:sintesy_app/providers/recording_state.dart';
import 'package:sintesy_app/providers/sintesy_state.dart';
import 'package:sintesy_app/providers/youtube_state.dart';
import 'package:sintesy_app/services/sintesy_sync_service.dart';
import 'package:sintesy_app/services/user_client.dart';
import 'package:sintesy_app/services/cubit.dart';
import 'package:sintesy_app/services/user_plan_manager.dart';
import 'package:sintesy_app/services/handle_task_service.dart';
import 'package:sintesy_app/src/customer_model.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/themes/light_theme.dart';
import 'package:sintesy_app/themes/dark_theme.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:sintesy_app/widgets/persistent_player.dart';
import 'firebase_options.dart';

final GlobalKey<ScaffoldMessengerState> scaffoldMessengerKey =
    GlobalKey<ScaffoldMessengerState>();
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

final customInternetChecker = InternetConnectionChecker.createInstance(
  addresses: [
    AddressCheckOption(
      uri: Uri.parse('https://api.sintesy.me'),
    ),
  ],
);

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Forçar orientação vertical apenas
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  Stripe.publishableKey =
      'pk_live_51QTwOrAEo9mrlp0PObYjyplK76SroaWmGB7xGOcG2MVRt0ZSpssILQTeDOMm1E0jqoqJgw9FSbSGYJpPhXCbOsAe00BK0Rvzcb';
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await Hive.initFlutter();
  Hive.registerAdapter(SintesyModelAdapter());
  Hive.registerAdapter(AttendeeBotModelAdapter());
  FlutterForegroundTask.initCommunicationPort();

  // Stop any running foreground service from previous sessions
  final HandleTaskService handleTaskService = HandleTaskService();
  await handleTaskService.stopService();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  Future<Customer?> checkPrefsForUser(BuildContext context) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var sharedToken = prefs.getString('token');

    if (sharedToken != null) {
      try {
        AuthAPI authAPI = AuthAPI();
        var req = await authAPI.getUser(sharedToken);
        if (req.statusCode == 202) {
          var user = Customer.fromReqBody(req.data);
          BlocProvider.of<CustomerCubit>(context).login(user);
          return user;
        }
      } on Exception catch (e) {
        print('Erro ao buscar usuário: $e');
      }
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        BlocProvider<CustomerCubit>(
          create: (context) => CustomerCubit(),
        ),
        ChangeNotifierProvider<RecordingState>(
          create: (context) => RecordingState(),
        ),
        ChangeNotifierProvider(create: (_) => SintesyState()),
        ChangeNotifierProvider<YouTubeState>(create: (_) => YouTubeState()),
        ChangeNotifierProvider<UserPlanProvider>(
            create: (_) => UserPlanProvider()),
        ChangeNotifierProvider(create: (_) => SintesySyncService()),
        ChangeNotifierProvider(create: (_) => InAppPurchaseProvider()),
        ChangeNotifierProvider(create: (_) => AudioState()),
        ChangeNotifierProvider<ConnectionProvider>(
            create: (context) => ConnectionProvider(customInternetChecker))
      ],
      child: MaterialApp(
        title: '',
        debugShowCheckedModeBanner: false,
        theme: lightTheme(),
        darkTheme: darkTheme(),
        themeMode: ThemeMode.system, // Segue o tema do sistema
        builder: (context, child) {
          return Scaffold(
            body: Column(
              children: [
                Expanded(child: child!),
                Consumer<AudioState>(
                  builder: (context, audioState, _) {
                    if (!audioState.isPlayerVisible) {
                      return const SizedBox.shrink();
                    }
                    if (audioState.currentAudioUrl == null) {
                      return const SizedBox.shrink();
                    }
                    return PersistentPlayer(
                      sintesyId: audioState.currentAudioUrl!,
                      title: audioState.currentTitle,
                    );
                  },
                ),
              ],
            ),
          );
        },
        initialRoute: '/',
        scaffoldMessengerKey: scaffoldMessengerKey,
        navigatorKey: navigatorKey,
        routes: {
          "/login": (context) => const WelcomePage(),
          "/home": (context) => const HomePage(),
          "/": (context) => const SplashPage(),
          "/s": (context) => const SubscriptionScreen(),
        },
      ),
    );
  }
}
