import 'dart:io';

import 'package:audio_duration/audio_duration.dart';
import 'package:hive/hive.dart';
import 'package:sintesy_app/models/sintesy_model.dart';

class SintesyCrud {
  final String _boxName = "sintesyBox";

  Future<Box<SintesyModel>> get _box async =>
      await Hive.openBox<SintesyModel>(_boxName);

  Future<void> add(SintesyModel sintesyModel) async {
    var box = await Hive.openBox<SintesyModel>(_boxName);

    if (sintesyModel.path != null &&
        sintesyModel.path!.isNotEmpty &&
        sintesyModel.duration == null) {
      final file = File(sintesyModel.path!);
      if (await file.exists()) {
        var duration = await AudioDuration.getAudioDuration(sintesyModel.path!);
        sintesyModel.duration = duration! ~/ 1000;
      }
    }

    await box.put(sintesyModel.uuid, sintesyModel);
  }

  Future<List<SintesyModel>> getAll() async {
    var box = await Hive.openBox<SintesyModel>(_boxName);
    return box.values.toList();
  }

  Future<void> update(String uuid, SintesyModel sintesyModel) async {
    var box = await Hive.openBox<SintesyModel>(_boxName);
    if (box.containsKey(uuid)) {
      await box.put(uuid, sintesyModel); // Atualiza usando o UUID como chave
    } else {
      throw Exception('Objeto com a chave $uuid não encontrado.');
    }
  }

  Future<void> delete(String uuid) async {
    var box = await Hive.openBox<SintesyModel>(_boxName);
    if (box.containsKey(uuid)) {
      await box.delete(uuid);
    } else {
      throw Exception('Objeto com a chave $uuid não encontrado.');
    }
  }

  Future<void> softDelete(String uuid) async {
    var box = await Hive.openBox<SintesyModel>(_boxName);
    if (box.containsKey(uuid)) {
      var sintesy = box.get(uuid);
      if (sintesy != null) {
        sintesy.deleted = true;
        await box.put(uuid, sintesy);
      }
    } else {
      throw Exception('Objeto com a chave $uuid não encontrado.');
    }
  }

  Future<void> clearAll() async {
    var box = await Hive.openBox<SintesyModel>(_boxName);
    await box.clear();
  }

  Future<List<SintesyModel>> getTodaySintesys() async {
    var box = await Hive.openBox<SintesyModel>(_boxName);

    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final todayEnd = DateTime(today.year, today.month, today.day, 23, 59, 59);

    return box.values
        .where((sintesy) =>
            sintesy.createdDate.isAfter(todayStart) &&
            sintesy.createdDate.isBefore(todayEnd))
        .toList();
  }

  Future<int> getTodayTotalDuration() async {
    var todaySintesys = await getTodaySintesys();
    return todaySintesys.fold<int>(
        0, (total, sintesy) => total + (sintesy.duration ?? 0));
  }

  Future<SintesyModel?> getById(String uuid) async {
    var box = await Hive.openBox<SintesyModel>(_boxName);
    return box.get(
        uuid); // Retorna o objeto SintesyModel correspondente ao UUID ou null se não existir
  }
}
