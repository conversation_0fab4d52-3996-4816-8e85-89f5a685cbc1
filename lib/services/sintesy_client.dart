import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:sintesy_app/services/base_api.dart';
import 'package:path/path.dart' as path;

class SintesyAPI extends BaseAPI {
  Future<http.Response> createSintesy(
      String id, String name, dynamic content) async {
    try {
      if (content is File) {
        final fileExtension = path
            .extension(content.path)
            .toLowerCase()
            .replaceAll('.', ''); // Extrai a extensão
        final presignedUrlResponse = await getPresignedUrl(fileExtension);
        final presignedUrlData = jsonDecode(presignedUrlResponse.body);

        if (presignedUrlResponse.statusCode < 200 ||
            presignedUrlResponse.statusCode >= 300) {
          throw Exception(
              'Erro ao obter URL pré-assinada: ${presignedUrlResponse.statusCode}, ${presignedUrlResponse.body}');
        }

        final presignedUrl = presignedUrlData['url'];
        final fileName = presignedUrlData['file_name'];
        final contentType = presignedUrlData['content_type'];

        await uploadFileToPresignedUrl(content, presignedUrl, contentType);

        return await createSintesyFromBucket(id, name, fileName);
      } else if (content is String) {
        return await createSintesyFromText(id, name, content);
      } else {
        throw Exception(
            'Tipo de conteúdo não suportado: ${content.runtimeType}');
      }
    } catch (e) {
      print('Ocorreu um erro ao criar a síntese: $e');
      rethrow;
    }
  }

  Future<http.Response> getPresignedUrl(String fileExtension) async {
    Uri url = Uri.parse(
        "${BaseAPI.base}/upload/get-presigned-url/?ext=$fileExtension");
    final response = await http.get(url, headers: await super.getHeaders());
    return response;
  }

  Future<void> uploadFileToPresignedUrl(
      File file, String presignedUrl, String contentType,
      {Function(double)? onProgress}) async {
    var request = http.Request('PUT', Uri.parse(presignedUrl));
    final bytes = await file.readAsBytes();
    request.bodyBytes = bytes;
    request.headers['Content-Type'] = contentType;

    final totalBytes = bytes.length;
    final httpClient = http.Client();
    final response = await httpClient.send(request);

    if (onProgress != null) {
      int received = 0;
      response.stream.listen(
        (value) {
          received += value.length;
          final progress = received / totalBytes;
          onProgress(progress);
        },
        onDone: () {
          onProgress(1.0);
        },
      );
    }

    if (response.statusCode < 200 || response.statusCode >= 300) {
      throw Exception(
          'Erro ao fazer upload do arquivo: ${response.statusCode}');
    }
    print('Upload do arquivo concluído com sucesso!');
  }

  Future<http.Response> createSintesyFromBucket(
      String id, String name, String sourceUrl) async {
    var request = http.MultipartRequest('POST', super.sintesyPath);
    request.headers.addAll(await super.getHeaders());
    request.fields['name'] = name;
    request.fields['id'] = id;
    request.fields['source_url'] = sourceUrl;
    request.fields['source'] = 'bucket';

    var response = await request.send();
    var responseBody = await http.Response.fromStream(response);

    if (response.statusCode < 200 || response.statusCode >= 300) {
      throw Exception(
          'Erro ao criar síntese a partir do bucket: ${response.statusCode}, ${responseBody.body}');
    }
    return responseBody;
  }

  Future<http.Response> createSintesyFromText(
      String id, String name, String text) async {
    var request = http.MultipartRequest('POST', super.sintesyPath);
    request.headers.addAll(await super.getHeaders());
    request.fields['name'] = name;
    request.fields['id'] = id;
    request.fields['text'] = text;

    var response = await request.send();
    var responseBody = await http.Response.fromStream(response);
    if (response.statusCode < 200 || response.statusCode >= 300) {
      throw Exception(
          'Erro ao criar sintese a partir de texto: ${response.statusCode}, ${responseBody.body}');
    }

    return responseBody;
  }

  Future<http.Response> getSintesys({int limit = 10, int offset = 0}) async {
    final queryParameters = {
      'limit': limit.toString(),
      'offset': offset.toString(),
    };
    final uri = Uri.parse("${BaseAPI.base}/sintesy/v2/")
        .replace(queryParameters: queryParameters);
    http.Response response =
        await http.get(uri, headers: await super.getHeaders());
    return response;
  }

  Future<http.Response> deleteSintesy(String id) async {
    try {
      final url = Uri.parse('${super.sintesyPath}/$id/');
      final response = await http.delete(
        url,
        headers: await super.getHeaders(),
      );
      if (response.statusCode == 404) {
        print(
            'Sintesy com ID $id não encontrado na API, considerando como deletado.');
        return http.Response('', 204);
      }

      if (response.statusCode < 200 || response.statusCode >= 300) {
        throw Exception(
            'Erro ao deletar sintesy: ${response.statusCode}, corpo: ${response.body}');
      }

      return response;
    } catch (e) {
      print('Ocorreu um erro ao deletar a sintesy: $e');
      rethrow;
    }
  }

  Future<http.Response> getPodcastPart(String sintesyId, int partNumber) async {
    final headers = await getHeaders();
    final uri = Uri.parse(
      '${BaseAPI.base}/sintesy/$sintesyId/podcast/part/$partNumber/',
    );
    print(uri);
    return http.get(uri, headers: headers);
  }
}
