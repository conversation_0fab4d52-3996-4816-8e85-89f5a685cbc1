import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:sintesy_app/services/base_api.dart';

class MeetingAPI extends BaseAPI {
  Future<http.Response> createFromMeeting({
    required String meetingUrl,
    String? sintesyName,
    String? botName,
    String? botImageType,
    String? botImageData,
  }) async {
    Map<String, dynamic> body = {
      'meeting_url': meetingUrl,
    };

    // Adiciona o nome da sintesy se fornecido
    if (sintesyName != null && sintesyName.isNotEmpty) {
      body['name'] = sintesyName;
    }

    // Adiciona o nome do bot se fornecido
    if (botName != null && botName.isNotEmpty) {
      body['bot_name'] = botName;
    }

    // Adiciona a imagem do bot se fornecida
    if (botImageType != null && botImageData != null) {
      body['bot_image'] = {
        'type': botImageType,
        'data': botImageData,
      };
    }

    http.Response response = await http.post(
      super.createSintesyCall,
      headers: await super.getHeaders(),
      body: json.encode(body),
    );

    return response;
  }

  /// Valida se a URL é do Meet ou Teams
  static bool isValidMeetingUrl(String url) {
    final meetRegex = RegExp(r'^https://meet\.google\.com/[a-z0-9-]+$');
    final teamsRegex = RegExp(r'^https://teams\.microsoft\.com/.*$');
    
    return meetRegex.hasMatch(url) || teamsRegex.hasMatch(url);
  }

  /// Detecta o tipo de reunião baseado na URL
  static String getMeetingType(String url) {
    if (url.contains('meet.google.com')) {
      return 'meet';
    } else if (url.contains('teams.microsoft.com')) {
      return 'teams';
    }
    return 'unknown';
  }
}
