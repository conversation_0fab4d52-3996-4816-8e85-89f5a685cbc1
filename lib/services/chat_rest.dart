import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sintesy_app/services/base_api.dart';

class SintesyChatAPI {
  final Dio _dio = Dio();

  Future<Map<String, dynamic>> askSintesy(String question) async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('token');
    final url = "${BaseAPI.base}/sintesy/ask/";

    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Token $token',
    };

    final data = jsonEncode({'question': question});

    final response = await _dio.post(
      url,
      options: Options(headers: headers),
      data: data,
    );

    if (response.statusCode == 200) {
      final responseData = response.data;
      return {
        'answer': responseData['answer'] ?? '',
        'sources': (responseData['sources'] as List<dynamic>?)
                ?.cast<Map<String, dynamic>>() ??
            [],
      };
    } else {
      throw Exception(
          'Failed to ask Sintesy: ${response.statusCode}, ${response.data}');
    }
  }
}
