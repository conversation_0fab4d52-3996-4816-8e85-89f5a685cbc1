import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/services/user_plan_manager.dart';
import 'package:sintesy_app/widgets/whatsapp_dialog.dart';

class WhatsAppService {
  static Future<void> checkAndShowWhatsAppDialog(BuildContext context) async {
    final userPlanProvider =
        Provider.of<UserPlanProvider>(context, listen: false);
    await Future.delayed(const Duration(milliseconds: 500));
    final userPlan = userPlanProvider.userPlan;

    if (userPlan == null || userPlan.whatsapp != null) {
      return;
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const WhatsAppDialog(),
      );
    });
  }
}
