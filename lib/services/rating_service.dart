import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:sintesy_app/services/base_api.dart';

enum RatingType {
  perfect,
  good,
  bad,
}

class RatingService extends BaseAPI {
  Future<bool> rateSintesy(String sintesyUuid, RatingType rating) async {
    try {
      final url = Uri.parse('${BaseAPI.base}/sintesy/$sintesyUuid/rating/');
      
      // Converter o tipo de avaliação para o formato esperado pela API
      String ratingValue;
      switch (rating) {
        case RatingType.perfect:
          ratingValue = 'perfect';
          break;
        case RatingType.good:
          ratingValue = 'good';
          break;
        case RatingType.bad:
          ratingValue = 'bad';
          break;
      }
      
      final response = await http.post(
        url,
        headers: await super.getHeaders(),
        body: jsonEncode({'rating': ratingValue}),
      );
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return true;
      } else {
        print('Erro ao avaliar sintesy: ${response.statusCode}, ${response.body}');
        return false;
      }
    } catch (e) {
      print('Ocorreu um erro ao avaliar a sintesy: $e');
      return false;
    }
  }
}