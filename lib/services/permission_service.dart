import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionService {
  BuildContext context;
  PermissionService(this.context);

  bool _isDialogOpen = false;

  Future<bool> requestPermissions() async {
    // Android 13+, you need to allow notification permission to display foreground service notification.
    //
    // iOS: If you need notification, ask for permission.

    for (var i = 0; i < 3; i++) {
      final NotificationPermission notificationPermission =
          await FlutterForegroundTask.checkNotificationPermission();
      if (notificationPermission != NotificationPermission.granted) {
        try {
          if (i < 2) {
            await FlutterForegroundTask.requestNotificationPermission();
          } else {
            _showPermissionDialog(
              'Permissão de Notificação',
              'Para que o aplicativo funcione corretamente, é necessário conceder permissão de notificação. Por favor, habilite-a nas configurações.',
            );
            return false;
          }
        } on PlatformException catch (e) {
          if (e.code == 'PermissionRequestCancelledException') {
            _showPermissionDialog(
              'Permissão de Notificação',
              'Para que o aplicativo funcione corretamente, é necessário conceder permissão de notificação. Por favor, habilite-a nas configurações.',
            );
            return false;
          } else {
            print('Erro de plataforma: ${e.message}');
          }
        }
      }
    }

    if (Platform.isAndroid) {
      for (var i = 0; i < 3; i++) {
        if (!await FlutterForegroundTask.isIgnoringBatteryOptimizations) {
          if (i < 2) {
            await FlutterForegroundTask.requestIgnoreBatteryOptimization();
          } else {}
        }
      }
    }

    final checkPermission = await Permission.microphone.request();
    if (checkPermission.isDenied ||
        checkPermission.isPermanentlyDenied ||
        checkPermission.isRestricted) {
      _showPermissionDialog(
        'Acesso ao Microfone',
        'Para que o aplicativo funcione corretamente, é necessário conceder permissão para usar o microfone. Por favor, habilite essa permissão nas configurações.',
      );
      return false;
    }
    return true;
  }

  void _showPermissionDialog(String title, String message) {
    if (_isDialogOpen) {
      return;
    }
    _isDialogOpen = true;
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Ok'),
            ),
            TextButton(
              onPressed: () {
                openAppSettings();
              },
              child: const Text('Abrir Configurações'),
            ),
          ],
        );
      },
    ).then((_) {
      _isDialogOpen = false; // Marca como fechado ao finalizar o diálogo
    });
  }
}
