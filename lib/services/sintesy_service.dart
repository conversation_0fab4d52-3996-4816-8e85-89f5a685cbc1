import 'package:intl/intl.dart';
import 'package:sintesy_app/models/sintesy_model.dart';
import 'package:sintesy_app/services/sintesy_local_crud.dart';

class SintesyService {
  final SintesyCrud _sintesyCrud = SintesyCrud();

  Future<SintesyModel> saveSintesyLocal(name, path) async {
    SintesyModel sintesy =
        SintesyModel(name: name, path: path, createdDate: DateTime.now());
    await _sintesyCrud.add(sintesy);
    return sintesy;
  }

  Future<List<SintesyModel>> getAll() {
    return _sintesyCrud.getAll();
  }

  // TODO remover e colocar em um arquivo utils
  String formatDataTimeToTitle(dataTime) {
    final formattedDate = DateFormat('HH:mm - yyyy/MM/dd').format(dataTime);
    return formattedDate;
  }
}
