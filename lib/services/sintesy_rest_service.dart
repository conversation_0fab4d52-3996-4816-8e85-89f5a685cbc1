import 'dart:convert';
import 'package:sintesy_app/services/sintesy_client.dart';
import 'package:sintesy_app/src/sintesy_model.dart';

class SintesyRestService {
  final apiClient = SintesyAPI();

  Future<int> createSintesy(String id, String name, dynamic content) async {
    var response = await apiClient.createSintesy(id, name, content);
    if (response.statusCode == 200 || response.statusCode == 201) {
      var jsonResponse = jsonDecode(response.body);
      Sintesy.fromJson(jsonResponse);
      return response.statusCode;
    } else if (response.statusCode == 403) {
      return 403;
    } else {
      return response.statusCode;
    }
  }

  Future<Map<String, dynamic>> getSintesys(
      {int limit = 10, int offset = 0}) async {
    var response = await apiClient.getSintesys(limit: limit, offset: offset);
    if (response.statusCode == 200) {
      String responseBodyUtf8 = utf8.decode(response.bodyBytes);
      return jsonDecode(responseBodyUtf8);
    } else {
      throw Exception(
          'Erro ao obter sínteses: ${response.statusCode}, ${response.body}');
    }
  }

  Future<bool> deleteSintesy(String id) async {
    try {
      final response = await apiClient.deleteSintesy(id);
      if (response.statusCode == 200 || response.statusCode == 204) {
        return true;
      } else {
        print(
            'Erro ao deletar sintesy: ${response.statusCode}, corpo: ${response.body}');
        return false;
      }
    } catch (e) {
      print('Erro ao deletar sintesy: $e');
      return false;
    }
  }
}
