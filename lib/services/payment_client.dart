import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:sintesy_app/services/base_api.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PaymentClient extends BaseAPI {
  final Dio _dio = Dio();

  Future<Map<String, dynamic>> createCustomer() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      var email = prefs.getString('email');
      final headers = await getHeaders();

      final response = await _dio.post(
        super.createCustomerPath.toString(),
        options: Options(headers: headers),
        data: json.encode({'email': email}),
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        return {
          'id': responseData['customer_id'],
          'client_secret': responseData['client_secret'],
        };
      } else {
        throw Exception(
            'Falha ao criar o cliente. Código de status: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao criar o cliente: $e');
    }
  }

  Future<Map<String, dynamic>> createSubscription(String customerId, {String? couponCode}) async {
    try {
      final headers = await getHeaders();

      final requestBody = {
        'customer_id': customerId,
      };

      // Adiciona cupom se fornecido
      if (couponCode != null && couponCode.isNotEmpty) {
        requestBody['coupon'] = couponCode;
      }

      final response = await _dio.post(
        super.createSubscriptionPath.toString(),
        options: Options(headers: headers),
        data: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        return responseData;
      } else {
        throw Exception(
            'Falha ao criar a assinatura. Código de status: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao criar a assinatura: $e');
    }
  }

  Future<Map<String, dynamic>?> validateCoupon(String couponCode) async {
    try {
      final headers = await getHeaders();

      final response = await _dio.post(
        super.validateCouponPath.toString(),
        options: Options(headers: headers),
        data: json.encode({
          'code': couponCode,
        }),
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['valid'] == true) {
          return responseData['coupon'];
        } else {
          throw Exception(responseData['message'] ?? responseData['error'] ?? 'Cupom inválido');
        }
      } else {
        throw Exception('Falha ao validar cupom. Código de status: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao validar cupom: $e');
    }
  }

  Future<Map<String, dynamic>> getSubscription() async {
    final headers = await getHeaders();

    final response = await _dio.get(
      super.getSubscriptionPath.toString(),
      options: Options(headers: headers),
    );

    return response.data;
  }
}
