import 'package:flutter/services.dart';

class ClipboardService {
  static final ClipboardService _instance = ClipboardService._internal();
  factory ClipboardService() => _instance;
  ClipboardService._internal();

  Future<String?> getYouTubeLinkFromClipboard() async {
    final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
    final clipboardText = clipboardData?.text;

    if (clipboardText != null &&
        (clipboardText.contains('youtube.com') ||
            clipboardText.contains('youtu.be'))) {
      return clipboardText;
    }

    return null;
  }

  Future<void> clearClipboard() async {
    await Clipboard.setData(const ClipboardData(text: ''));
  }
}
