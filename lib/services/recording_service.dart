import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/main.dart';
import 'package:sintesy_app/models/sintesy_model.dart';
import 'package:sintesy_app/providers/recording_state.dart';
import 'package:path/path.dart' as path;
import 'package:sintesy_app/providers/sintesy_state.dart';
import 'package:sintesy_app/services/sintesy_local_crud.dart';
import 'package:sintesy_app/services/sintesy_service.dart';
import 'package:sintesy_app/services/sintesy_sync_service.dart';
import 'package:sintesy_app/services/user_plan_manager.dart';
import 'package:sintesy_app/utils/call_plan.dart';

class RecordingService {
  final RecordingState recordingState;
  final BuildContext context;

  RecordingService(this.recordingState, this.context);

  final _audioRecorder = FlutterSoundRecorder();
  Timer? _timer;
  int _elapsedSeconds = 0;
  final SintesyCrud sintesyCrud = SintesyCrud();
  final SintesyState sintesyState = SintesyState();
  SintesySyncService sintesySyncService = SintesySyncService();
  final SintesyService sintesyService = SintesyService();

  String _formatDataTimeToPath(dataTime) {
    final formattedDate = DateFormat('HHmm_yyyyMMdd').format(dataTime);
    return formattedDate;
  }

  Future<String> _generateFilePath(dataTime) async {
    final documentDir = await getApplicationDocumentsDirectory();
    final formattedDate = _formatDataTimeToPath(dataTime);
    final fileName = '$formattedDate.wav';
    final filePath = path.join(documentDir.path, fileName);
    return filePath;
  }

  Future<void> startRecording() async {
    await _audioRecorder.openRecorder();
    await _audioRecorder
        .setSubscriptionDuration(const Duration(milliseconds: 50));
    final now = DateTime.now();
    final filePath = await _generateFilePath(now);
    recordingState.setFilePath(filePath);
    await _audioRecorder.startRecorder(
      codec: Codec.defaultCodec,
      toFile: filePath,
    );

    _audioRecorder.onProgress!.listen((event) {
      if (event.decibels != null) {
        recordingState.updateDbLevel(event.decibels!);
      }
    });

    recordingState.startRecording();
    _elapsedSeconds = 0;
    _startRecordingTimer();
  }

  Future<SintesyModel?> stopRecording() async {
    _timer?.cancel();
    await _audioRecorder.stopRecorder();
    await _audioRecorder.closeRecorder();
    recordingState.stopRecording();
    recordingState.resetAmplitudeLevels();

    if (_elapsedSeconds < 5) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            backgroundColor: const Color.fromARGB(255, 255, 102, 102),
            content: const Text("Áudio muito curto."),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.only(
              bottom: MediaQuery.of(context).size.height - 80,
              left: 20,
              right: 20,
            ),
          ),
        );
      }
      await File(recordingState.filePath).delete();
      return null;
    }
    var file = File(recordingState.filePath);
    if (!await file.exists()) {
      throw Exception("Arquivo não encontrado: ${recordingState.filePath}");
    }
    final now = DateTime.now();
    final title = sintesyService.formatDataTimeToTitle(now);
    SintesyModel sintesy = new SintesyModel(
        name: title, path: recordingState.filePath, createdDate: now);
    sintesyState.sintesys = await sintesyService.getAll();
    return sintesy;
  }

  Future<void> _saveSintesyLocally(SintesyModel sintesy) async {
    try {
      await sintesyCrud.add(sintesy);
    } catch (e) {
      print("Erro ao salvar síntese localmente: $e");
    }
  }

  Future<void> pauseRecording() async {
    await _audioRecorder.pauseRecorder();
    recordingState.pauseRecording();
  }

  Future<void> resumeRecording() async {
    await _audioRecorder.resumeRecorder();
    recordingState.startRecording();
  }

  void _startRecordingTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (recordingState.recorderStatus == 'record') {
        _elapsedSeconds++;
        recordingState.addOneSecond();
        if (context.mounted) {
          final userPlanProvider =
              Provider.of<UserPlanProvider>(context, listen: false);
          int limite = (userPlanProvider.userPlan?.limitMinutesTime ?? 15);
          bool isPremium = userPlanProvider.userPlan?.isPremium ?? false;

          if (_elapsedSeconds / 60 >= limite && !isPremium) {
            await stopRecording();

            if (!context.mounted) {
              return;
            }

            scaffoldMessengerKey.currentState?.showSnackBar(
              SnackBar(
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                        'Limite de ${limite.toString()}m por gravação atingido'),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextButton(
                          onPressed: () {
                            callPlan(context);
                          },
                          child: const Text('Se torne PRO'),
                        ),
                      ],
                    ),
                  ],
                ),
                duration: const Duration(seconds: 5),
              ),
            );
            return;
          }
        } else {
          timer.cancel();
        }
      }
    });
  }


}
