import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:sintesy_app/services/user_plan_client.dart';
import 'package:sintesy_app/src/user_plan_model.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class UserPlanProvider with ChangeNotifier {
  UserPlan? _userPlan;
  UserPlan? get userPlan => _userPlan;
  Timer? _timer;
  final UserPlanAPI _authAPI = UserPlanAPI();
  static const _userPlanKey = 'userPlan';

  UserPlanProvider() {
    _userPlan = UserPlan(
      isPremium: true,
      limitReachedDate: null,
      limitMinutesTime: null,
      minuteLimit: null,
      limited: null,
      whatsapp: null,
      whatsappEnabled: null,
    );

    _loadUserPlanFromSharedPreferences();
  }

  void setUserPlan(UserPlan plan) {
    _userPlan = plan;
    Future.microtask(() {
      notifyListeners();
    });
    _saveUserPlanToSharedPreferences(plan);
  }

  Future<void> fetchUserPlan() async {
    try {
      Response response = await _authAPI.getUserPlan();
      if (response.statusCode == 200) {
        var json = response.data;
        UserPlan newPlan = UserPlan.fromJson(json);
        if (_userPlan != newPlan) {
          setUserPlan(newPlan);
          print('Plano do usuário atualizado: $_userPlan');
        } else {
          print('Plano do usuário igual: $_userPlan');
        }
      } else {
        print('Falha ao buscar plano do usuário: ${response.statusCode}');
      }
    } catch (e) {
      print('Erro ao buscar plano do usuário: $e');
    }
  }

  void startPlanUpdates() {
    if (_timer != null && _timer!.isActive) {
      return;
    }
    if (_userPlan == null) {
      setUserPlan(UserPlan.fromJson(
          {"email": "", "is_premium": false, "limit_reached_date": null}));
    }
    fetchUserPlan();
    _timer = Timer.periodic(const Duration(seconds: 20), (timer) async {
      print('Verificando plano do usuário...');
      await fetchUserPlan();
    });
  }

  void stopPlanUpdates() {
    _timer?.cancel();
    _timer = null;
  }

  set userPlan(UserPlan? plan) {
    _userPlan = plan;
  }

  Future<void> _loadUserPlanFromSharedPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = prefs.getString(_userPlanKey);

    if (jsonString != null) {
      try {
        final json = jsonDecode(jsonString);
        _userPlan = UserPlan.fromJson(json);
        notifyListeners();
        print('Plano do usuário carregado do SharedPreferences: $_userPlan');
      } catch (e) {
        print('Erro ao desserializar o plano do SharedPreferences: $e');
      }
    } else {
      print('Nenhum plano encontrado no SharedPreferences.');
    }
  }

  Future<void> _saveUserPlanToSharedPreferences(UserPlan plan) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = jsonEncode(plan.toJson());
    await prefs.setString(_userPlanKey, jsonString);
    print('Plano do usuário salvo no SharedPreferences: $plan');
  }
}
