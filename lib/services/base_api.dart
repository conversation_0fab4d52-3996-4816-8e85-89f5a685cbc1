import 'package:shared_preferences/shared_preferences.dart';

class BaseAPI {
  static Uri base = Uri.parse("https://api.sintesy.me");
  Uri oauthPath = Uri.parse("$base/auth/oauth");
  Uri authPath = Uri.parse("$base/auth");
  Uri createCustomerPath = Uri.parse("$base/payment/create-customer");
  Uri createSubscriptionPath = Uri.parse("$base/payment/create-subscription");
  Uri validateCouponPath = Uri.parse("$base/payment/validate-coupon");
  Uri userPath = Uri.parse("$base/user");
  Uri sintesyPath = Uri.parse("$base/sintesy");
  Uri sintesyPathv2 = Uri.parse("$base/sintesy/v2/");
  Uri userPlanPath = Uri.parse("$base/user/plan-info");
  Uri getSubscriptionPath = Uri.parse("$base/payment/subscription");
  Uri createSintesyYoutube = Uri.parse("$base/sintesy/youtube/");
  Uri createSintesyCall = Uri.parse("$base/sintesy/call/");

  Future<Map<String, String>> getHeaders() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('token');

    Map<String, String> headerBase = {
      "Content-Type": "application/json; charset=UTF-8",
    };

    if (token != null) {
      headerBase["Authorization"] = "Token $token";
    }

    return headerBase;
  }
}
