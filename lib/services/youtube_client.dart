import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:sintesy_app/services/base_api.dart';

class YoutubeAPI extends BaseAPI {
  Future<http.Response> createFromYoutube(url, name, uuid) async {
    http.Response response = await http.post(super.createSintesyYoutube,
        headers: await super.getHeaders(),
        body: json.encode({'url': url, 'name': name, 'uuid': uuid}));
    return response;
  }
}
