import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/main.dart';
import 'package:sintesy_app/models/sintesy_model.dart';
import 'package:sintesy_app/providers/connection_checker.dart';
import 'package:sintesy_app/providers/sintesy_state.dart';
import 'package:sintesy_app/services/sintesy_client.dart';
import 'package:sintesy_app/services/sintesy_local_crud.dart';
import 'package:sintesy_app/services/sintesy_rest_service.dart';
import 'package:sintesy_app/services/sintesy_service.dart';
import 'package:sintesy_app/src/sintesy_model.dart';

class SintesySyncService with ChangeNotifier {
  final SintesyAPI apiClient = SintesyAPI();
  final SintesyCrud localCrud = SintesyCrud();
  final SintesyRestService sintesyRestService = SintesyRestService();
  final SintesyService sintesyService = SintesyService();
  final SintesyState sintesyState = SintesyState();
  Timer? _syncTimer;

  Future<void> initialSync(BuildContext context) async {
    bool hasMore = true;
    int offset = 0;
    const limit = 100;

    while (hasMore) {
      try {
        var apiResponse =
            await sintesyRestService.getSintesys(limit: limit, offset: offset);
        List<dynamic> results = apiResponse['results'];
        List<Sintesy> sintesys =
            results.map((json) => Sintesy.fromJson(json)).toList();

        if (apiResponse['next'] == null) {
          hasMore = false;
        }
        await syncSintesys(sintesys);

        offset += limit;
      } catch (e) {
        print("Erro durante a sincronização inicial: $e");
        hasMore = false;
      }
    }
    sintesyState.sintesys = await sintesyService.getAll();
    notifyListeners();
  }

  Future<void> syncSintesys(List<Sintesy> apiSintesys) async {
    List<SintesyModel> localSintesys = await localCrud.getAll();

    Map<String, Sintesy> apiSintesysMap = {
      for (var sintesy in apiSintesys) sintesy.id: sintesy
    };

    Map<String, SintesyModel> localSintesysMap = {
      for (var localSintesy in localSintesys) localSintesy.uuid: localSintesy
    };

    for (var localSintesy in localSintesys) {
      if (localSintesy.deleted == true &&
          apiSintesysMap.containsKey(localSintesy.uuid)) {
        await _deleteSintesyFromAPI(localSintesy.uuid);
        await _deleteSintesyLocally(localSintesy.uuid);
        continue;
      }
    }

    for (var apiSintesy in apiSintesys) {
      if (localSintesysMap.containsKey(apiSintesy.id)) {
        var localSintesy = localSintesysMap[apiSintesy.id]!;
        if (localSintesy.deleted != true) {
          await _updateLocalSintesy(localSintesy, apiSintesy);
        }
      } else {
        await _saveSintesyLocally(apiSintesy);
      }
    }

    for (var localSintesy in localSintesys) {
      if (!apiSintesysMap.containsKey(localSintesy.uuid)) {
        var existingSintesy = await localCrud.getById(localSintesy.uuid);
        if (existingSintesy != null &&
            existingSintesy.deleted != true &&
            existingSintesy.summary == null) {
          await uploadLocalSintesy(localSintesy);
        } else {
          // print(
          //     "Síntese local com UUID ${localSintesy.uuid} não encontrada. Upload cancelado.");
        }
      }
    }
    notifyListeners();
  }

  Future<List<Sintesy>> _getSintesysFromAPI() async {
    try {
      var apiSintesys = await sintesyRestService.getSintesys();
      List<dynamic> results = apiSintesys['results'];
      List<Sintesy> sintesys =
          results.map((json) => Sintesy.fromJson(json)).toList();
      return sintesys;
    } catch (e) {
      print("Erro ao obter sínteses da API: $e");
      return [];
    }
  }

  Future<void> _saveSintesyLocally(Sintesy sintesy) async {
    try {
      AttendeeBotModel? attendeeBot;
      if (sintesy.attendeeBot != null) {
        attendeeBot = AttendeeBotModel.fromJson(sintesy.attendeeBot!);
      }

      var localSintesy = SintesyModel(
          name: sintesy.name,
          path: '',
          uuid: sintesy.id,
          duration: sintesy.duration,
          summary: sintesy.summary,
          text: sintesy.text,
          createdDate: sintesy.createdDate,
          source: sintesy.source,
          status: sintesy.status,
          attendeeBot: attendeeBot);
      await localCrud.add(localSintesy);
    } catch (e) {
      print("Erro ao salvar síntese localmente: $e");
    }
    notifyListeners();
  }

  Future<void> _updateLocalSintesy(
      SintesyModel localSintesy, Sintesy apiSintesy) async {
    localSintesy.name = apiSintesy.name;
    localSintesy.duration = apiSintesy.duration;
    localSintesy.summary = apiSintesy.summary;
    localSintesy.text = apiSintesy.text;
    localSintesy.summarySpeechUrl = apiSintesy.summarySpeechUrl;
    localSintesy.mindmap = apiSintesy.mindmap;
    localSintesy.checklist = apiSintesy.checklist;
    localSintesy.roadmap = apiSintesy.roadmap;
    localSintesy.source = apiSintesy.source;
    localSintesy.status = apiSintesy.status;

    // Atualiza attendeeBot se presente
    if (apiSintesy.attendeeBot != null) {
      localSintesy.attendeeBot = AttendeeBotModel.fromJson(apiSintesy.attendeeBot!);
    }

    await localCrud.update(apiSintesy.id, localSintesy);
    sintesyState.sintesys = await sintesyService.getAll();
    notifyListeners();
  }

  Future<void> deleteSintesy(BuildContext context, String id) async {
    if (Provider.of<ConnectionProvider>(context, listen: false).status) {
      await _softDeleteSintesyLocally(id);
      await _deleteSintesyFromAPI(id);
    }
    final sintesyState = Provider.of<SintesyState>(context, listen: false);
    sintesyState.sintesys = await sintesyService.getAll();
    notifyListeners();
  }

  Future<void> _deleteSintesyFromAPI(String id) async {
    try {
      final success = await sintesyRestService.deleteSintesy(id);
      if (success) {
        print("Síntese deletada da API com sucesso.");
      } else {
        print("Erro ao deletar síntese da API.");
      }
    } catch (e) {
      print("Erro ao deletar síntese da API: $e");
    }
  }

  Future<void> _deleteSintesyLocally(String id) async {
    try {
      await localCrud.delete(id);
      sintesyState.sintesys = await localCrud.getAll();
      print("Síntese deletada localmente com sucesso.");
    } catch (e) {
      print("Erro ao deletar síntese localmente: $e");
    }
  }

  Future<void> _softDeleteSintesyLocally(String id) async {
    try {
      await localCrud.softDelete(id);
      sintesyState.sintesys = await localCrud.getAll();
      print("Síntese deletada localmente com sucesso.");
    } catch (e) {
      print("Erro ao deletar síntese localmente: $e");
    }
  }

  Future<int?> uploadLocalSintesy(SintesyModel localSintesy) async {
    //verificar se ja existe na base, se não criar
    try {
      var sintesy = await localCrud.getById(localSintesy.uuid);
      if (sintesy == null) {
        await localCrud.add(localSintesy);
      }
    } catch (e) {
      print("Erro ao obter síntese da API: $e");
    }
    //localSintesy.localStatus!='failed_send' ||
    if (localSintesy.deleted == true ||
        localSintesy.localStatus == 'sending' ||
        localSintesy.localStatus == 'sent') {
      print('skip from status');
      return null;
    }
    BuildContext? context = navigatorKey.currentContext;
    bool hasConnection =
        Provider.of<ConnectionProvider>(context!, listen: false).status;
    if (hasConnection) {
      if (localSintesy.path != null && localSintesy.path != "") {
        var file = File(localSintesy.path!);
        localSintesy.localStatus = 'sending';
        await localCrud.update(localSintesy.uuid, localSintesy);
        final statusCode = await sintesyRestService.createSintesy(
            localSintesy.uuid, localSintesy.name, file);

        if (statusCode == 403) {
          return 403;
        }

        if (statusCode == 200 || statusCode == 201) {
          localSintesy.localStatus = 'sent';
          await localCrud.update(localSintesy.uuid, localSintesy);
          await file.delete();
          sintesyState.sintesys = await sintesyService.getAll();
          notifyListeners();
          print("Síntese local enviada para a API com sucesso");
          return statusCode;
        } else {
          localSintesy.localStatus = 'failed_send';
          await localCrud.update(localSintesy.uuid, localSintesy);
          print('erro ao enviar');
          return statusCode;
        }
      } else if (localSintesy.text != null && localSintesy.text != "") {
        final statusCode = await sintesyRestService.createSintesy(
            localSintesy.uuid, localSintesy.name, localSintesy.text);

        if (statusCode == 403) {
          return 403;
        }

        sintesyState.sintesys = await sintesyService.getAll();
        notifyListeners();
        return statusCode;
      }
    }
    return null;
  }

  bool _needsUpdate(SintesyModel localSintesy, Sintesy apiSintesy) {
    return localSintesy.name != apiSintesy.name ||
        localSintesy.duration != apiSintesy.duration ||
        localSintesy.summary != apiSintesy.summary ||
        localSintesy.summarySpeechUrl != apiSintesy.summarySpeechUrl;
  }

  Future<void> syncNewSintesys(BuildContext context) async {
    print('sync...');
    final sintesyState = Provider.of<SintesyState>(context, listen: false);

    if (Provider.of<ConnectionProvider>(context, listen: false).status) {
      List<Sintesy> apiSintesys = await _getSintesysFromAPI();
      await syncSintesys(apiSintesys);
    } else {
      print("Sem conexão, sincronização adiada.");
    }
    sintesyState.sintesys = await sintesyService.getAll();
    notifyListeners();
  }

  Future<void> syncSintesysTimer(BuildContext context) async {
    if (_syncTimer != null && _syncTimer!.isActive) {
      print("O Timer já está ativo. Não será iniciado novamente.");
      return;
    }
    await initialSync(context);
    _syncTimer = Timer.periodic(
      const Duration(seconds: 2),
      (Timer formTimer) async {
        await syncNewSintesys(context);
      },
    );

    print("Timer de sincronização iniciado.");
  }

  void stopSyncSintesys() {
    if (_syncTimer != null && _syncTimer!.isActive) {
      _syncTimer!.cancel();
      _syncTimer = null;
      print("Sincronização parada com sucesso.");
    } else {
      print("Nenhuma sincronização ativa para ser parada.");
    }
  }
}
