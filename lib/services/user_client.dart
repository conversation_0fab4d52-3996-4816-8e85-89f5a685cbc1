
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:sintesy_app/services/base_api.dart';

class AuthAPI extends BaseAPI {
  final Dio _dio = Dio();

  Future<Response> auth(String email, String password) async {
    var body = jsonEncode({'email': email, 'password': password});
    final headers = await super.getHeaders();

    Response response = await _dio.post(
      super.authPath.toString(),
      data: body,
      options: Options(headers: headers),
    );

    return response;
  }

  Future<Response> oauth(String code) async {
    var body = jsonEncode({'code': code});
    final headers = await super.getHeaders();

    return await _dio.post(
      super.oauthPath.toString(),
      data: body,
      options: Options(headers: headers),
    );
  }

  Future<Response> getUser(String token) async {
    final headers = await super.getHeaders();

    Response response = await _dio.get(
      super.userPath.toString(),
      options: Options(headers: headers),
    );

    return response;
  }
}