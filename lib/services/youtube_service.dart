import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:sintesy_app/models/sintesy_model.dart';
import 'package:sintesy_app/services/sintesy_local_crud.dart';
import 'package:sintesy_app/services/sintesy_sync_service.dart';
import 'package:youtube_explode_dart/youtube_explode_dart.dart';
import 'package:path_provider/path_provider.dart';

class AudioDownloadService {
  final YoutubeExplode yt;
  SintesySyncService sintesySyncService = SintesySyncService();

  AudioDownloadService(this.yt);

  Future<String> downloadAudio(Video video) async {
    try {
      var id = video.id;
      print('Título do vídeo: ${video.title}');
      final documentDir = await getApplicationDocumentsDirectory();
      const fileName = 'youtubevideo.mp4';
      final filePath = path.join(documentDir.path, fileName);
      final outputFile = File(filePath);

      if (await outputFile.exists()) {
        SintesyModel sintesy =
            await _saveSintesyLocal(video.title, outputFile.path);
        await sintesySyncService.uploadLocalSintesy(sintesy);
        return "Criando Sintesy...";
      }

      final manifest = await yt.videos.streams.getManifest(id);
      final audio = manifest.audioOnly;

      if (audio.isNotEmpty) {
        final audioStreamInfo = audio.first;
        final audioStream = yt.videos.streamsClient.get(audioStreamInfo);
        print('Baixando áudio para: ${outputFile.path}');
        final output = outputFile.openWrite();
        await audioStream.pipe(output);
        await output.flush();
        await output.close();

        print('Download concluído: ${outputFile.path}');
      } else {
        return "Vídeo não pode ser baixado";
      }

      SintesyModel sintesy =
          await _saveSintesyLocal(video.title, outputFile.path);
      await sintesySyncService.uploadLocalSintesy(sintesy);
      return "Criando Sintesy...";
    } catch (e) {
      return "Vídeo não pode ser baixado";
    }
  }

  Future<Video> getVideo(String videoUrl) async {
    if (videoUrl.contains('/live')) {
      final videoId = extractVideoId(videoUrl);
      return await yt.videos.get(videoId);
    } else {
      return await yt.videos.get(videoUrl);
    }
  }

  String extractVideoId(String link) {
    final uri = Uri.parse(link);
    final segments = uri.pathSegments;
    if (segments.isNotEmpty && segments.contains('live')) {
      final index = segments.indexOf('live');
      if (index + 1 < segments.length) {
        return segments[index + 1];
      }
    }
    throw ArgumentError('URL não contém um ID de vídeo válido.');
  }

  Future<SintesyModel> _saveSintesyLocal(String name, String path) async {
    final SintesyCrud sintesyCrud = SintesyCrud();
    SintesyModel sintesy =
        SintesyModel(name: name, path: path, createdDate: DateTime.now());
    await sintesyCrud.add(sintesy);
    return sintesy;
  }
}
