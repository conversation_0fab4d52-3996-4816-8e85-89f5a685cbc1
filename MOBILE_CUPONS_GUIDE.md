# Guia de Cupons para Mobile - Sintesy

Este guia mostra como implementar o sistema de cupons no aplicativo mobile do Sintesy.

## Fluxo de Pagamento Mobile

### 1. Validar Cupom (Opcional)

Antes de iniciar o processo de pagamento, valide o cupom inserido pelo usuário:

```javascript
// React Native / JavaScript
const validateCoupon = async (couponCode) => {
    try {
        const response = await fetch('https://api.sintesy.me/payment/validate-coupon', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Token ${userToken}`
            },
            body: JSON.stringify({
                code: couponCode
            })
        });

        const data = await response.json();
        
        if (response.ok && data.valid) {
            // Cupom válido - mostrar desconto para o usuário
            console.log(`Cupom válido: ${data.coupon.discount_display} de desconto`);
            return data.coupon;
        } else {
            // Cupom inválido - mostrar erro
            console.log(`Erro: ${data.message || data.error}`);
            return null;
        }
    } catch (error) {
        console.error('Erro ao validar cupom:', error);
        return null;
    }
};
```

### 2. Criar Subscription com Cupom

```javascript
const createSubscriptionWithCoupon = async (customerId, couponCode = null) => {
    try {
        const requestBody = {
            customer_id: customerId,
            price_id: 'price_1R3lXDAEo9mrlp0PNiQC9JkK' // ID do preço no Stripe
        };

        // Adiciona cupom se fornecido
        if (couponCode) {
            requestBody.coupon = couponCode;
        }

        const response = await fetch('https://api.sintesy.me/payment/create-subscription', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Token ${userToken}`
            },
            body: JSON.stringify(requestBody)
        });

        const data = await response.json();
        
        if (response.ok) {
            return {
                subscriptionId: data.subscription_id,
                status: data.status,
                couponApplied: data.coupon_applied
            };
        } else {
            throw new Error(data.error || 'Erro ao criar subscription');
        }
    } catch (error) {
        console.error('Erro ao criar subscription:', error);
        throw error;
    }
};
```
