import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sintesy_app/widgets/custom_bottom_navigation_bar.dart';
import 'package:sintesy_app/themes/light_theme.dart';

void main() {
  group('CustomBottomNavigationBar Expandable Tests', () {
    testWidgets('shows all buttons on large screens (>=400px)', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: lightTheme(),
          home: Scaffold(
            body: MediaQuery(
              data: const MediaQueryData(size: Size(450, 800)),
              child: CustomBottomNavigationBar(
                onRecordTap: () {},
                onYoutubeTap: () {},
                onNoteTap: () {},
                onFileTap: () {},
                onMeetingTap: () {},
              ),
            ),
          ),
        ),
      );

      // Deve mostrar todos os 5 botões em uma linha
      expect(find.text('GRAVAÇÃO'), findsOneWidget);
      expect(find.text('YOUTUBE'), findsOneWidget);
      expect(find.text('REUNIÃO'), findsOneWidget);
      expect(find.text('ANOTAÇÃO'), findsOneWidget);
      expect(find.text('ARQUIVO'), findsOneWidget);
      expect(find.text('MAIS'), findsNothing);
    });

    testWidgets('shows expandable toolbar on medium screens (350-399px)', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: lightTheme(),
          home: Scaffold(
            body: MediaQuery(
              data: const MediaQueryData(size: Size(375, 800)),
              child: CustomBottomNavigationBar(
                onRecordTap: () {},
                onYoutubeTap: () {},
                onNoteTap: () {},
                onFileTap: () {},
                onMeetingTap: () {},
              ),
            ),
          ),
        ),
      );

      // Deve mostrar 3 botões principais + botão "MAIS"
      expect(find.text('GRAVAÇÃO'), findsOneWidget);
      expect(find.text('YOUTUBE'), findsOneWidget);
      expect(find.text('REUNIÃO'), findsOneWidget);
      expect(find.text('MAIS'), findsOneWidget);
      expect(find.text('ANOTAÇÃO'), findsNothing); // Deve estar oculto inicialmente
      expect(find.text('ARQUIVO'), findsNothing); // Deve estar oculto inicialmente
    });

    testWidgets('shows expandable toolbar on small screens (<350px)', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: lightTheme(),
          home: Scaffold(
            body: MediaQuery(
              data: const MediaQueryData(size: Size(320, 800)),
              child: CustomBottomNavigationBar(
                onRecordTap: () {},
                onYoutubeTap: () {},
                onNoteTap: () {},
                onFileTap: () {},
                onMeetingTap: () {},
              ),
            ),
          ),
        ),
      );

      // Deve mostrar apenas 2 botões principais + botão "MAIS"
      expect(find.text('GRAVAÇÃO'), findsOneWidget);
      expect(find.text('YOUTUBE'), findsOneWidget);
      expect(find.text('MAIS'), findsOneWidget);
      expect(find.text('REUNIÃO'), findsNothing); // Deve estar oculto inicialmente
      expect(find.text('ANOTAÇÃO'), findsNothing); // Deve estar oculto inicialmente
      expect(find.text('ARQUIVO'), findsNothing); // Deve estar oculto inicialmente
    });

    testWidgets('expansão da toolbar funciona corretamente em tela média', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: lightTheme(),
          home: Scaffold(
            body: MediaQuery(
              data: const MediaQueryData(size: Size(375, 800)),
              child: CustomBottomNavigationBar(
                onRecordTap: () {},
                onYoutubeTap: () {},
                onNoteTap: () {},
                onFileTap: () {},
                onMeetingTap: () {},
              ),
            ),
          ),
        ),
      );

      // Inicialmente, botões secundários devem estar ocultos
      expect(find.text('ANOTAÇÃO'), findsNothing);
      expect(find.text('ARQUIVO'), findsNothing);
      expect(find.text('MAIS'), findsOneWidget);

      // Toca no botão "MAIS" para expandir
      await tester.tap(find.text('MAIS'));
      await tester.pumpAndSettle();

      // Agora os botões secundários devem estar visíveis
      expect(find.text('ANOTAÇÃO'), findsOneWidget);
      expect(find.text('ARQUIVO'), findsOneWidget);
      expect(find.text('MENOS'), findsOneWidget);

      // Toca no botão "MENOS" para recolher
      await tester.tap(find.text('MENOS'));
      await tester.pumpAndSettle();

      // Botões secundários devem estar ocultos novamente
      expect(find.text('ANOTAÇÃO'), findsNothing);
      expect(find.text('ARQUIVO'), findsNothing);
      expect(find.text('MAIS'), findsOneWidget);
    });

    testWidgets('expansão da toolbar funciona corretamente em tela pequena', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: lightTheme(),
          home: Scaffold(
            body: MediaQuery(
              data: const MediaQueryData(size: Size(320, 800)),
              child: CustomBottomNavigationBar(
                onRecordTap: () {},
                onYoutubeTap: () {},
                onNoteTap: () {},
                onFileTap: () {},
                onMeetingTap: () {},
              ),
            ),
          ),
        ),
      );

      // Inicialmente, botões secundários devem estar ocultos
      expect(find.text('REUNIÃO'), findsNothing);
      expect(find.text('ANOTAÇÃO'), findsNothing);
      expect(find.text('ARQUIVO'), findsNothing);
      expect(find.text('MAIS'), findsOneWidget);

      // Toca no botão "MAIS" para expandir
      await tester.tap(find.text('MAIS'));
      await tester.pumpAndSettle();

      // Agora todos os botões secundários devem estar visíveis
      expect(find.text('REUNIÃO'), findsOneWidget);
      expect(find.text('ANOTAÇÃO'), findsOneWidget);
      expect(find.text('ARQUIVO'), findsOneWidget);
      expect(find.text('MENOS'), findsOneWidget);
    });
  });
}
