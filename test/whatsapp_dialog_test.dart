import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/services/user_plan_manager.dart';
import 'package:sintesy_app/src/user_plan_model.dart';
import 'package:sintesy_app/widgets/whatsapp_dialog.dart';

void main() {
  testWidgets('WhatsApp dialog shows correctly', (WidgetTester tester) async {
    // Create a UserPlanProvider with a plan that has no WhatsApp number
    final userPlanProvider = UserPlanProvider();
    userPlanProvider.setUserPlan(UserPlan(
      isPremium: true,
      limitReachedDate: null,
      limitMinutesTime: 500,
      minuteLimit: 500,
      limited: false,
      whatsapp: null,
      whatsappEnabled: null,
    ));

    // Build our app and trigger a frame
    await tester.pumpWidget(
      MaterialApp(
        home: ChangeNotifierProvider<UserPlanProvider>.value(
          value: userPlanProvider,
          child: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (context) => const WhatsAppDialog(),
                    );
                  },
                  child: const Text('Show Dialog'),
                );
              },
            ),
          ),
        ),
      ),
    );

    // Tap the button to show the dialog
    await tester.tap(find.text('Show Dialog'));
    await tester.pumpAndSettle();

    // Verify that the dialog is shown
    expect(find.text('Digite seu número do WhatsApp'), findsOneWidget);
    expect(find.text('Use um número real! Coloque seu DDD + número para não ocorrer erros!'), findsOneWidget);
    expect(find.text('BR +55'), findsOneWidget);
    expect(find.text('Habilitar integração com WhatsApp'), findsOneWidget);
    expect(find.text('Salvar'), findsOneWidget);
  });
}
