name: sintesy_app
description: "Sintesy app"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 46.0.0+1

environment:
  sdk: ^3.4.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  audio_duration: ^0.0.3
  audioplayers: ^6.1.0
  build_runner: ^2.4.11
  cupertino_icons: ^1.0.8
  file_picker: ^10.1.7
  firebase_core: ^3.8.0
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.1
  flutter_foreground_task: ^9.1.0
  flutter_keyboard_visibility: ^6.0.0
  flutter_markdown: ^0.7.3+1
  flutter_sound: 9.18.0
  flutter_stripe: ^11.3.0
  font_awesome_flutter: ^10.8.0
  google_fonts: ^6.2.1
  google_sign_in: ^6.2.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  hive_generator: ^2.0.1
  http: ^1.2.2
  in_app_purchase: ^3.2.0
  internet_connection_checker: ^3.0.1
  intl: ^0.20.2
  jetpack: ^1.0.7
  path_provider: ^2.1.4
  permission_handler: ^12.0.0+1
  provider: ^6.1.2
  shared_preferences: ^2.3.2
  url_launcher: ^6.3.1
  social_login_buttons: ^1.0.7
  timelines: ^0.1.0
  uuid: ^4.5.0
  youtube_explode_dart: ^2.3.5
  flutter_inappwebview: ^6.1.5
  animated_text_kit: ^4.2.2
  receive_sharing_intent:
      git:
        url: https://github.com/KasemJaffer/receive_sharing_intent
        ref: master
  dio: ^5.8.0+1

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.

  path: any
  image_picker: ^1.1.2
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  assets:
    - assets/images/
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
  #%20Verifique%20a%20vers%C3%A3o%20mais%20recente%20no%20pub.dev
